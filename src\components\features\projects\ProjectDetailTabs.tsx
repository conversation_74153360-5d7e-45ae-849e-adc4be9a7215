import React, { useState } from "react";
import {
  Users,
  AlertCircle,
  Code,
  Activity,
  ExternalLink,
  Copy,
  CheckCircle,
  Clock,
  Github,
  FileText,
  Terminal,
  GitCommit
} from "lucide-react";
import { Project, Issue, Maintainer, Commit } from "../../../types/project.types";

// Contribute Tab Component
export function ContributeTab({ project }: { project: Project }) {
  const [copiedToClipboard, setCopiedToClipboard] = useState(false);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopiedToClipboard(true);
    setTimeout(() => setCopiedToClipboard(false), 2000);
  };

  return (
    <div className="space-y-6">
      {/* Contribution Guide */}
      {project.contributionGuide && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Users className="h-5 w-5 mr-2 text-blue-500" />
            How to Contribute
          </h3>
          <div className="prose prose-invert max-w-none">
            <pre className="whitespace-pre-wrap text-gray-300 text-sm leading-relaxed">
              {project.contributionGuide}
            </pre>
          </div>
        </div>
      )}

      {/* Quick Clone */}
      {project.repositoryUrl && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Terminal className="h-5 w-5 mr-2 text-green-500" />
            Quick Start
          </h3>
          <div className="bg-gray-900 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-400">Clone the repository:</span>
              <button
                onClick={() => copyToClipboard(`git clone ${project.repositoryUrl}`)}
                className="flex items-center text-sm text-blue-400 hover:text-blue-300"
              >
                <Copy className="h-3 w-3 mr-1" />
                {copiedToClipboard ? 'Copied!' : 'Copy'}
              </button>
            </div>
            <code className="text-green-400 text-sm">
              git clone {project.repositoryUrl}
            </code>
          </div>
        </div>
      )}

      {/* Maintainers */}
      {project.maintainers && project.maintainers.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Users className="h-5 w-5 mr-2 text-purple-500" />
            Project Maintainers
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {project.maintainers.map((maintainer) => (
              <div key={maintainer.id} className="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg">
                <img
                  src={maintainer.avatar}
                  alt={maintainer.name}
                  className="w-12 h-12 rounded-full"
                />
                <div className="flex-1">
                  <h4 className="font-medium text-gray-200">{maintainer.name}</h4>
                  <p className="text-sm text-gray-400">{maintainer.role}</p>
                  <p className="text-xs text-gray-500">{maintainer.contributions} contributions</p>
                </div>
                <a
                  href={maintainer.githubUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-gray-300"
                >
                  <Github className="h-4 w-4" />
                </a>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Issues Tab Component
export function IssuesTab({ project }: { project: Project }) {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'text-green-400 bg-green-900/20 border-green-500/30';
      case 'Medium': return 'text-yellow-400 bg-yellow-900/20 border-yellow-500/30';
      case 'Hard': return 'text-red-400 bg-red-900/20 border-red-500/30';
      default: return 'text-gray-400 bg-gray-900/20 border-gray-500/30';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 30) return `${diffDays} days ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  };

  return (
    <div className="space-y-6">
      {/* Good First Issues */}
      {project.goodFirstIssues && project.goodFirstIssues.length > 0 ? (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-orange-500" />
              Good First Issues ({project.goodFirstIssues.length})
            </h3>
            {project.repositoryUrl && (
              <a
                href={`${project.repositoryUrl}/issues?q=is%3Aissue+is%3Aopen+label%3A%22good+first+issue%22`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:text-blue-300 text-sm flex items-center"
              >
                View all on GitHub <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            )}
          </div>

          <div className="space-y-4">
            {project.goodFirstIssues.map((issue) => (
              <div key={issue.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="text-lg font-medium text-gray-200 mb-2">{issue.title}</h4>
                    <p className="text-gray-400 text-sm mb-3">{issue.description}</p>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full border ${getDifficultyColor(issue.difficulty)} ml-4`}>
                    {issue.difficulty}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span className="flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {issue.estimatedTime}
                    </span>
                    <span>by {issue.author}</span>
                    <span>{formatDate(issue.createdAt)}</span>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="flex flex-wrap gap-1">
                      {issue.labels.map((label) => (
                        <span
                          key={label}
                          className="px-2 py-1 text-xs rounded bg-gray-700 text-gray-300"
                        >
                          {label}
                        </span>
                      ))}
                    </div>
                    <a
                      href={issue.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm inline-flex items-center"
                    >
                      View Issue <ExternalLink className="h-3 w-3 ml-1" />
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">No Good First Issues</h3>
          <p className="text-gray-500 mb-4">This project doesn't have any labeled good first issues yet.</p>
          {project.repositoryUrl && (
            <a
              href={`${project.repositoryUrl}/issues`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 inline-flex items-center"
            >
              View all issues <ExternalLink className="h-3 w-3 ml-1" />
            </a>
          )}
        </div>
      )}
    </div>
  );
}

// Setup Tab Component
export function SetupTab({ project }: { project: Project }) {
  const [copiedToClipboard, setCopiedToClipboard] = useState('');

  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text);
    setCopiedToClipboard(id);
    setTimeout(() => setCopiedToClipboard(''), 2000);
  };

  return (
    <div className="space-y-6">
      {/* Setup Instructions */}
      {project.setupInstructions && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Code className="h-5 w-5 mr-2 text-blue-500" />
            Setup Instructions
          </h3>
          <div className="prose prose-invert max-w-none">
            <pre className="whitespace-pre-wrap text-gray-300 text-sm leading-relaxed">
              {project.setupInstructions}
            </pre>
          </div>
        </div>
      )}

      {/* Quick Commands */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Terminal className="h-5 w-5 mr-2 text-green-500" />
          Quick Commands
        </h3>
        <div className="space-y-4">
          {[
            { label: 'Clone repository', command: `git clone ${project.repositoryUrl || 'https://github.com/example/repo.git'}`, id: 'clone' },
            { label: 'Install dependencies', command: 'npm install', id: 'install' },
            { label: 'Start development', command: 'npm run dev', id: 'dev' },
            { label: 'Run tests', command: 'npm test', id: 'test' },
            { label: 'Build project', command: 'npm run build', id: 'build' }
          ].map(({ label, command, id }) => (
            <div key={id} className="bg-gray-900 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-400">{label}:</span>
                <button
                  onClick={() => copyToClipboard(command, id)}
                  className="flex items-center text-sm text-blue-400 hover:text-blue-300"
                >
                  <Copy className="h-3 w-3 mr-1" />
                  {copiedToClipboard === id ? 'Copied!' : 'Copy'}
                </button>
              </div>
              <code className="text-green-400 text-sm">{command}</code>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Activity Tab Component
export function ActivityTab({ project }: { project: Project }) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Recent Commits */}
      {project.recentCommits && project.recentCommits.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <GitCommit className="h-5 w-5 mr-2 text-green-500" />
            Recent Commits
          </h3>
          <div className="space-y-3">
            {project.recentCommits.map((commit) => (
              <div key={commit.id} className="flex items-start space-x-4 p-4 bg-gray-700 rounded-lg">
                <GitCommit className="h-4 w-4 text-green-400 mt-1 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-gray-200 text-sm">{commit.message}</p>
                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                    <span>by {commit.author}</span>
                    <span>{formatDate(commit.date)}</span>
                  </div>
                </div>
                <a
                  href={commit.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-gray-300 flex-shrink-0"
                >
                  <ExternalLink className="h-3 w-3" />
                </a>
              </div>
            ))}
          </div>
          {project.repositoryUrl && (
            <div className="mt-4 text-center">
              <a
                href={`${project.repositoryUrl}/commits`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:text-blue-300 text-sm inline-flex items-center"
              >
                View all commits <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </div>
          )}
        </div>
      )}

      {/* Project Stats */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Activity className="h-5 w-5 mr-2 text-purple-500" />
          Project Activity
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-blue-400">{project.stars}</div>
            <div className="text-sm text-gray-400">Stars</div>
          </div>
          <div className="text-center p-4 bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-green-400">{project.forks}</div>
            <div className="text-sm text-gray-400">Forks</div>
          </div>
          <div className="text-center p-4 bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-orange-400">{project.issues}</div>
            <div className="text-sm text-gray-400">Open Issues</div>
          </div>
          <div className="text-center p-4 bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-purple-400">{project.contributors}</div>
            <div className="text-sm text-gray-400">Contributors</div>
          </div>
        </div>
      </div>
    </div>
  );
}
