// Application-wide constants

// App Configuration
export const APP_CONFIG = {
  name: 'IDXCodeHub',
  description: 'Discover and contribute to open source projects',
  version: '1.0.0',
  author: 'IDXCodeHub Team',
  repository: 'https://github.com/example/idxcodehub',
  website: 'https://idxcodehub.dev',
} as const;

// API Configuration
export const API_CONFIG = {
  baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
  timeout: 10000,
  retryAttempts: 3,
} as const;

// GitHub OAuth Configuration
export const GITHUB_CONFIG = {
  clientId: import.meta.env.VITE_GITHUB_CLIENT_ID || 'demo_client_id',
  redirectUri: import.meta.env.VITE_GITHUB_REDIRECT_URI || `${window.location.origin}/auth/callback`,
  scope: 'user:email read:user',
} as const;

// Routes
export const ROUTES = {
  HOME: '/',
  PROJECTS: '/projects',
  PROJECT_DETAILS: '/project/:id',
  PROFILE: '/profile',
  DOCUMENTATION: '/documentation',
  OPEN_SOURCE_GUIDE: '/open-source-guide',
  AUTH_CALLBACK: '/auth/callback',
  NOT_FOUND: '*',
} as const;

// Navigation Items
export const NAVIGATION_ITEMS = [
  { path: ROUTES.HOME, label: 'Home', icon: 'Home' },
  { path: ROUTES.PROJECTS, label: 'Projects', icon: 'Code2' },
  { path: ROUTES.DOCUMENTATION, label: 'Docs', icon: 'Book' },
  { path: ROUTES.OPEN_SOURCE_GUIDE, label: 'Guide', icon: 'BookOpen' },
] as const;

// Search Configuration
export const SEARCH_CONFIG = {
  debounceDelay: 300,
  minQueryLength: 2,
  maxResults: 50,
  defaultSortBy: 'stars',
  defaultSortOrder: 'desc',
} as const;

// Filter Options
export const FILTER_OPTIONS = {
  languages: [
    'JavaScript',
    'TypeScript',
    'Python',
    'Java',
    'Go',
    'Rust',
    'C++',
    'C#',
    'PHP',
    'Ruby',
    'Swift',
    'Kotlin',
    'Dart',
    'Vue',
    'React',
    'Angular',
  ],
  difficulties: ['Beginner', 'Intermediate', 'Advanced'],
  domains: [
    'Web Development',
    'Mobile Development',
    'Backend',
    'Frontend',
    'Data Science',
    'AI/ML',
    'DevOps',
    'Game Development',
    'Desktop Applications',
    'Blockchain',
    'IoT',
    'Security',
  ],
  activityLevels: ['Low', 'Medium', 'High'],
  sortOptions: [
    { value: 'stars', label: 'Stars' },
    { value: 'forks', label: 'Forks' },
    { value: 'updated', label: 'Last Updated' },
    { value: 'name', label: 'Name' },
    { value: 'contributors', label: 'Contributors' },
  ],
} as const;

// Theme Configuration
export const THEME_CONFIG = {
  default: 'dark',
  options: ['light', 'dark', 'system'],
  storageKey: 'idxcodehub-theme',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  THEME: 'idxcodehub-theme',
  SEARCH_HISTORY: 'search_history',
  BOOKMARKS: 'bookmarks',
  CONTRIBUTIONS: 'contributions',
  BADGES: 'badges',
  ACTIVITY: 'activity',
  PREFERENCES: 'user_preferences',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  AUTH_FAILED: 'Authentication failed. Please try again.',
  PROJECT_NOT_FOUND: 'Project not found.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  PROJECT_BOOKMARKED: 'Project bookmarked successfully!',
  PROJECT_UNBOOKMARKED: 'Project removed from bookmarks.',
  PROFILE_UPDATED: 'Profile updated successfully!',
  CONTRIBUTION_TRACKED: 'Contribution tracked!',
  BADGE_EARNED: 'Congratulations! You earned a new badge!',
} as const;

// Animation Durations (in milliseconds)
export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  EXTRA_SLOW: 1000,
} as const;

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;

// Z-Index Layers
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
} as const;

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
  ENABLE_ERROR_REPORTING: import.meta.env.VITE_ENABLE_ERROR_REPORTING === 'true',
  ENABLE_BETA_FEATURES: import.meta.env.VITE_ENABLE_BETA_FEATURES === 'true',
  ENABLE_NOTIFICATIONS: true,
  ENABLE_DARK_MODE: true,
  ENABLE_SEARCH_SUGGESTIONS: true,
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 12,
  MAX_PAGE_SIZE: 50,
  SHOW_SIZE_OPTIONS: [12, 24, 48],
} as const;

// File Upload
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
} as const;

// Rate Limiting
export const RATE_LIMITS = {
  SEARCH_REQUESTS_PER_MINUTE: 60,
  API_REQUESTS_PER_MINUTE: 100,
  BOOKMARK_ACTIONS_PER_MINUTE: 30,
} as const;
