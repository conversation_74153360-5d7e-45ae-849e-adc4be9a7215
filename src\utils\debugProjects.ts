// Debug utility to help troubleshoot project loading issues
import { projectService } from '@/services/projectService';
import { PROJECTS_DATA } from '@/constants/projects.data';

export async function debugProjectLoading() {
  console.log('🐛 DEBUG: Project Loading Analysis');
  console.log('=====================================');
  
  // Check static projects
  console.log('\n📊 Static Projects Available:');
  PROJECTS_DATA.forEach((project, index) => {
    console.log(`${index + 1}. ID: "${project.id}" | Title: "${project.title}"`);
  });
  
  // Test loading each static project
  console.log('\n🧪 Testing Static Project Loading:');
  for (const project of PROJECTS_DATA) {
    try {
      const result = await projectService.getProjectById(project.id);
      console.log(`✅ ${project.id}: ${result.success ? 'SUCCESS' : 'FAILED'} - ${result.message || result.error}`);
    } catch (error) {
      console.log(`❌ ${project.id}: ERROR - ${error}`);
    }
  }
  
  // Check cache stats
  console.log('\n💾 Cache Statistics:');
  const cacheStats = projectService.getCacheStats();
  console.log(`Cache size: ${cacheStats.size}`);
  console.log('Cache keys:', cacheStats.keys);
  
  // Test a few GitHub project scenarios
  console.log('\n🐙 Testing GitHub Project Scenarios:');
  const githubTestIds = ['github-123', 'github-456'];
  for (const testId of githubTestIds) {
    try {
      const result = await projectService.getProjectById(testId);
      console.log(`${testId}: ${result.success ? 'SUCCESS' : 'FAILED'} - ${result.message || result.error}`);
    } catch (error) {
      console.log(`${testId}: ERROR - ${error}`);
    }
  }
  
  console.log('\n🎯 Debug Analysis Complete!');
}

export async function testProjectSearch() {
  console.log('🔍 Testing Project Search...');
  
  try {
    const searchResult = await projectService.searchProjects({
      query: 'react',
      minStars: 10,
    });
    
    console.log(`Search result: ${searchResult.success ? 'SUCCESS' : 'FAILED'}`);
    if (searchResult.success) {
      console.log(`Found ${searchResult.data.projects.length} projects`);
      searchResult.data.projects.slice(0, 3).forEach((project, index) => {
        console.log(`${index + 1}. ID: "${project.id}" | Title: "${project.title}" | Source: ${project.source}`);
      });
    } else {
      console.log('Search error:', searchResult.error);
    }
  } catch (error) {
    console.log('Search failed:', error);
  }
}

// Make available in browser console for debugging
if (typeof window !== 'undefined') {
  (window as any).debugProjects = {
    debugProjectLoading,
    testProjectSearch,
    projectService,
  };
  
  console.log('🐛 Debug tools available in console:');
  console.log('- window.debugProjects.debugProjectLoading()');
  console.log('- window.debugProjects.testProjectSearch()');
}
