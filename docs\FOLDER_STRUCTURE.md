# 📁 Folder Structure Guide

This document explains the professional folder structure of IDXCodeHub and the reasoning behind each organizational decision.

## 🏗️ Overall Architecture

```
src/
├── components/           # All reusable UI components
├── pages/               # Route-level page components
├── hooks/               # Custom React hooks
├── contexts/            # React contexts for global state
├── services/            # External API and service integrations
├── utils/               # Pure utility functions and helpers
├── types/               # TypeScript type definitions
├── constants/           # Application constants and static data
└── styles/              # Global styles and theme definitions
```

## 📦 Detailed Breakdown

### `/src/components/`
Organized by component type and feature domain:

```
components/
├── ui/                  # Basic, reusable UI components
│   ├── Button.tsx       # Generic button component
│   ├── Input.tsx        # Form input component
│   ├── Badge.tsx        # Status/label badge component
│   └── index.ts         # Barrel exports
├── layout/              # Layout and navigation components
│   ├── Layout.tsx       # Main app layout wrapper
│   ├── Header.tsx       # Site header/navigation
│   ├── Footer.tsx       # Site footer
│   └── Sidebar.tsx      # Sidebar navigation
├── features/            # Feature-specific components
│   ├── auth/            # Authentication-related components
│   │   ├── LoginForm.tsx
│   │   ├── SignupForm.tsx
│   │   └── AuthGuard.tsx
│   ├── projects/        # Project-related components
│   │   ├── ProjectCard.tsx
│   │   ├── ProjectGrid.tsx
│   │   ├── ProjectDetailTabs.tsx
│   │   └── ProjectFilters.tsx
│   ├── profile/         # User profile components
│   │   ├── ProfileHeader.tsx
│   │   ├── ProfileTabs.tsx
│   │   ├── BadgeDisplay.tsx
│   │   └── ContributionChart.tsx
│   └── search/          # Search and filtering components
│       ├── SearchBar.tsx
│       ├── AdvancedFilters.tsx
│       ├── SearchResults.tsx
│       └── SearchStats.tsx
└── common/              # Shared components across features
    ├── LoadingSpinner.tsx
    ├── ErrorBoundary.tsx
    ├── Modal.tsx
    └── Tooltip.tsx
```

**Reasoning**: 
- **ui/**: Contains pure, reusable components with no business logic
- **layout/**: Structural components that define page layout
- **features/**: Business logic components organized by domain
- **common/**: Shared utilities that don't fit in ui/ but are used across features

### `/src/pages/`
Route-level components that represent full pages:

```
pages/
├── Home.tsx             # Landing page
├── Projects.tsx         # Project listing page
├── ProjectDetails.tsx   # Individual project page
├── Profile.tsx          # User profile page
├── Documentation.tsx    # Documentation page
├── OpenSourceGuide.tsx  # Open source guide
├── AuthCallback.tsx     # OAuth callback handler
└── ErrorPage.tsx        # 404 and error pages
```

**Reasoning**: Each file represents a distinct route/page in the application.

### `/src/hooks/`
Custom React hooks for reusable stateful logic:

```
hooks/
├── useSearch.ts         # Search functionality and state
├── useLocalStorage.ts   # LocalStorage management
├── useDebounce.ts       # Debouncing utility
├── useAuth.ts           # Authentication state (if extracted)
└── index.ts             # Barrel exports
```

**Reasoning**: Separates stateful logic from components for better reusability and testing.

### `/src/contexts/`
React contexts for global application state:

```
contexts/
├── AuthContext.tsx      # User authentication state
├── ThemeContext.tsx     # Theme and appearance settings
├── SearchContext.tsx    # Global search state (if needed)
└── index.ts             # Barrel exports
```

**Reasoning**: Centralized state management for data that needs to be shared across components.

### `/src/services/`
External API integrations and service layers:

```
services/
├── authService.ts       # GitHub OAuth and user management
├── contributionService.ts # Contribution tracking
├── projectService.ts    # Project data management
├── apiClient.ts         # Base API client configuration
└── index.ts             # Barrel exports
```

**Reasoning**: Abstracts external dependencies and provides clean interfaces for data operations.

### `/src/utils/`
Pure utility functions and helpers:

```
utils/
├── demoData.ts          # Demo data for development
├── dateUtils.ts         # Date formatting and manipulation
├── stringUtils.ts       # String processing utilities
├── validationUtils.ts   # Form validation helpers
└── index.ts             # Barrel exports
```

**Reasoning**: Pure functions that can be easily tested and reused throughout the application.

### `/src/types/`
TypeScript type definitions organized by domain:

```
types/
├── project.types.ts     # Project-related interfaces
├── user.types.ts        # User and authentication types
├── api.types.ts         # API request/response types
├── common.types.ts      # Shared/generic types
└── index.ts             # Barrel exports
```

**Reasoning**: Centralized type definitions improve maintainability and provide clear contracts.

### `/src/constants/`
Application constants and static data:

```
constants/
├── app.constants.ts     # App configuration and settings
├── projects.data.ts     # Static project data
├── routes.constants.ts  # Route definitions
├── theme.constants.ts   # Theme and styling constants
└── index.ts             # Barrel exports
```

**Reasoning**: Centralizes configuration and makes it easy to modify application behavior.

## 🎯 Benefits of This Structure

### 1. **Scalability**
- Easy to add new features without restructuring
- Clear separation of concerns
- Modular architecture supports team development

### 2. **Maintainability**
- Predictable file locations
- Consistent naming conventions
- Clear dependencies between modules

### 3. **Developer Experience**
- Fast file navigation with clear hierarchy
- Barrel exports reduce import complexity
- TypeScript support with proper type organization

### 4. **Testing**
- Easy to test individual components and utilities
- Clear separation makes mocking straightforward
- Isolated business logic in services and hooks

## 📋 Naming Conventions

### Files and Folders
- **Components**: PascalCase (e.g., `ProjectCard.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useSearch.ts`)
- **Services**: camelCase with service suffix (e.g., `authService.ts`)
- **Types**: camelCase with `.types.ts` suffix (e.g., `project.types.ts`)
- **Constants**: camelCase with `.constants.ts` suffix
- **Folders**: camelCase or kebab-case for multi-word names

### Exports
- **Default exports**: For main component/function of the file
- **Named exports**: For utilities and multiple exports
- **Barrel exports**: Use `index.ts` files to re-export from folders

## 🔄 Import Patterns

### Absolute Imports from src/
```typescript
// ✅ Good - Clear, absolute paths
import { Button } from 'src/components/ui';
import { useAuth } from 'src/contexts/AuthContext';
import { Project } from 'src/types/project.types';

// ❌ Avoid - Relative paths for distant files
import { Button } from '../../../components/ui/Button';
```

### Barrel Exports
```typescript
// In src/components/ui/index.ts
export { Button } from './Button';
export { Input } from './Input';
export { Badge } from './Badge';

// Usage
import { Button, Input, Badge } from 'src/components/ui';
```

## 🚀 Migration Guide

If you're restructuring an existing project:

1. **Create the new folder structure**
2. **Move files to appropriate locations**
3. **Update import paths systematically**
4. **Create barrel export files**
5. **Update build configuration if needed**
6. **Test thoroughly to ensure no broken imports**

## 📚 Additional Resources

- [React File Structure Best Practices](https://reactjs.org/docs/faq-structure.html)
- [TypeScript Project Structure](https://www.typescriptlang.org/docs/handbook/project-references.html)
- [Clean Architecture in Frontend](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
