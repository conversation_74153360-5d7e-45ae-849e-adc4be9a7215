# GitHub API Configuration
# Optional: GitHub Personal Access Token for higher rate limits
# Create one at: https://github.com/settings/tokens
# Permissions needed: public_repo (for public repositories)
VITE_GITHUB_TOKEN=your_github_token_here

# GitHub OAuth Configuration (for user authentication)
VITE_GITHUB_CLIENT_ID=your_github_client_id
VITE_GITHUB_REDIRECT_URI=http://localhost:5173/auth/callback

# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api

# Analytics (optional)
VITE_ENABLE_ANALYTICS=false

# Development
NODE_ENV=development
