import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import {
  Star,
  GitBranch,
  Eye,
  MessageSquare,
  Download,
  ExternalLink,
  Github,
  Play,
  Book,
  BookOpen,
  Users,
  Clock,
  AlertCircle,
  CheckCircle,
  Code,
  FileText,
  Calendar,
  ArrowLeft,
  Copy,
  Heart,
  Share2,
  Shield,
  Activity
} from "lucide-react";
import { Project, Issue, Maintainer, Commit } from "@/types/project.types";
import { ContributeTab, IssuesTab, SetupTab, ActivityTab } from "@/components/features/projects/ProjectDetailTabs";
import { CenteredLoadingSpinner, CenteredErrorMessage } from "@/components/ui";
import { useProjectDetails } from "@/hooks/useProjects";
import { useAuth, useProjectTracking } from "@/contexts/AuthContext";

export function ProjectDetails() {
  const { id } = useParams<{ id: string }>();
  const [activeTab, setActiveTab] = useState('overview');
  const [copiedToClipboard, setCopiedToClipboard] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);

  const { user, isAuthenticated, bookmarkProject, unbookmarkProject } = useAuth();
  const { trackProjectView, trackProjectStar } = useProjectTracking();

  // Use the new dynamic project details hook
  const { project, loading, error, refreshProject } = useProjectDetails(id || '');

  // Track project view when component mounts
  useEffect(() => {
    if (project && user) {
      trackProjectView(project.id, project.title);
    }
  }, [project, user, trackProjectView]);

  // Show loading state
  if (loading) {
    return <CenteredLoadingSpinner text="Loading project details..." />;
  }

  // Show error state
  if (error) {
    return (
      <CenteredErrorMessage
        title="Failed to load project"
        message={error}
        onRetry={refreshProject}
        retryText="Try Again"
      />
    );
  }

  // Show not found state
  if (!project) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-300 mb-4">Project Not Found</h1>
        <p className="text-gray-500 mb-6">The project you're looking for doesn't exist.</p>
        <Link
          to="/projects"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Projects
        </Link>
      </div>
    );
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopiedToClipboard(true);
    setTimeout(() => setCopiedToClipboard(false), 2000);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': case 'Easy': return 'text-green-400 bg-green-900/20 border-green-500/30';
      case 'Intermediate': case 'Medium': return 'text-yellow-400 bg-yellow-900/20 border-yellow-500/30';
      case 'Advanced': case 'Hard': return 'text-red-400 bg-red-900/20 border-red-500/30';
      default: return 'text-gray-400 bg-gray-900/20 border-gray-500/30';
    }
  };

  const getActivityColor = (level: string) => {
    switch (level) {
      case 'High': return 'text-green-400';
      case 'Medium': return 'text-yellow-400';
      case 'Low': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const handleBookmark = async () => {
    if (!isAuthenticated || !project) return;

    try {
      if (isBookmarked) {
        await unbookmarkProject(project.id);
        setIsBookmarked(false);
      } else {
        await bookmarkProject(project.id);
        setIsBookmarked(true);
      }
    } catch (error) {
      console.error('Failed to bookmark project:', error);
    }
  };

  const handleStar = async () => {
    if (!project) return;

    if (user) {
      await trackProjectStar(project.id, project.title);
    }

    // In a real app, this would call the GitHub API to star the repository
    if (project.repositoryUrl) {
      window.open(project.repositoryUrl, '_blank');
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Book },
    { id: 'contribute', label: 'How to Contribute', icon: Users },
    { id: 'issues', label: 'Good First Issues', icon: AlertCircle },
    { id: 'setup', label: 'Setup Guide', icon: Code },
    { id: 'activity', label: 'Recent Activity', icon: Activity }
  ];
  return (
    <div className="space-y-8">
      {/* Back Navigation */}
      <div className="flex items-center space-x-4">
        <Link
          to="/projects"
          className="flex items-center text-gray-400 hover:text-gray-300 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Projects
        </Link>
      </div>

      {/* Hero Section */}
      <div className="relative h-80 rounded-lg overflow-hidden">
        <img
          src={project.image}
          alt={project.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/50 to-transparent" />
        <div className="absolute bottom-0 left-0 right-0 p-8">
          <div className="flex flex-col lg:flex-row lg:items-end lg:justify-between gap-6">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <span className={`px-3 py-1 text-sm rounded-full border ${getDifficultyColor(project.difficulty)}`}>
                  {project.difficulty}
                </span>
                <span className="px-3 py-1 text-sm rounded-full bg-blue-600 text-white">
                  {project.language}
                </span>
                <span className={`flex items-center text-sm ${getActivityColor(project.activityLevel)}`}>
                  <span className="w-2 h-2 rounded-full bg-current mr-2"></span>
                  {project.activityLevel} Activity
                </span>
              </div>
              <h1 className="text-4xl font-bold mb-3">{project.title}</h1>
              <p className="text-xl text-gray-300 mb-4">{project.longDescription || project.description}</p>
              <div className="flex items-center space-x-6 text-gray-400">
                <span className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  Updated {formatDate(project.lastUpdated)}
                </span>
                <span className="flex items-center">
                  <Shield className="h-4 w-4 mr-1" />
                  {project.license}
                </span>
                <span className="flex items-center">
                  <Users className="h-4 w-4 mr-1" />
                  {project.contributors} contributors
                </span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              {project.repositoryUrl && (
                <a
                  href={project.repositoryUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-gray-800 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center justify-center transition-colors"
                >
                  <Github className="h-4 w-4 mr-2" />
                  View on GitHub
                </a>
              )}
              {project.demoUrl && (
                <a
                  href={project.demoUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center justify-center transition-colors"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Live Demo
                </a>
              )}
              {project.documentationUrl && (
                <a
                  href={project.documentationUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center justify-center transition-colors"
                >
                  <Book className="h-4 w-4 mr-2" />
                  Documentation
                </a>
              )}

              {/* Star and Bookmark buttons */}
              <button
                onClick={handleStar}
                className="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center justify-center transition-colors"
              >
                <Star className="h-4 w-4 mr-2" />
                Star
              </button>

              {isAuthenticated && (
                <button
                  onClick={handleBookmark}
                  className={`px-6 py-3 rounded-lg font-medium inline-flex items-center justify-center transition-colors ${
                    isBookmarked
                      ? 'bg-green-600 hover:bg-green-700 text-white'
                      : 'bg-gray-600 hover:bg-gray-700 text-white'
                  }`}
                >
                  <BookOpen className="h-4 w-4 mr-2" />
                  {isBookmarked ? 'Bookmarked' : 'Bookmark'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-6 gap-4">
        <StatCard icon={Star} value={project.stars} label="Stars" color="text-yellow-400" />
        <StatCard icon={GitBranch} value={project.forks} label="Forks" color="text-blue-400" />
        <StatCard icon={MessageSquare} value={project.issues} label="Issues" color="text-orange-400" />
        <StatCard icon={Users} value={project.contributors} label="Contributors" color="text-green-400" />
        <StatCard icon={Eye} value={Math.floor(project.stars * 4.2)} label="Views" color="text-purple-400" />
        <StatCard icon={Download} value={Math.floor(project.stars * 2.1)} label="Downloads" color="text-pink-400" />
      </div>

      {/* Technologies */}
      {project.technologies && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Code className="h-5 w-5 mr-2 text-blue-500" />
            Technologies Used
          </h3>
          <div className="flex flex-wrap gap-2">
            {project.technologies.map((tech) => (
              <span
                key={tech}
                className="px-3 py-1 rounded-full bg-gray-700 text-gray-300 text-sm border border-gray-600"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === 'overview' && <OverviewTab project={project} />}
        {activeTab === 'contribute' && <ContributeTab project={project} />}
        {activeTab === 'issues' && <IssuesTab project={project} />}
        {activeTab === 'setup' && <SetupTab project={project} />}
        {activeTab === 'activity' && <ActivityTab project={project} />}
      </div>
    </div>
  );
}

function StatCard({ icon: Icon, value, label, color }: {
  icon: any;
  value: number;
  label: string;
  color: string;
}) {
  return (
    <div className="bg-gray-800 rounded-lg p-4 text-center border border-gray-700">
      <Icon className={`h-5 w-5 mx-auto mb-2 ${color}`} />
      <div className="text-xl font-semibold">{value.toLocaleString()}</div>
      <div className="text-sm text-gray-400">{label}</div>
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ project }: { project: Project }) {
  return (
    <div className="space-y-6">
      {/* README Content */}
      {project.readme && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-500" />
            README
          </h3>
          <div className="prose prose-invert max-w-none">
            <pre className="whitespace-pre-wrap text-gray-300 text-sm leading-relaxed">
              {project.readme}
            </pre>
          </div>
        </div>
      )}

      {/* Project Structure */}
      {project.projectStructure && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Code className="h-5 w-5 mr-2 text-green-500" />
            Project Structure
          </h3>
          <div className="prose prose-invert max-w-none">
            <pre className="whitespace-pre-wrap text-gray-300 text-sm leading-relaxed">
              {project.projectStructure}
            </pre>
          </div>
        </div>
      )}

      {/* Prerequisites */}
      {project.prerequisites && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <CheckCircle className="h-5 w-5 mr-2 text-yellow-500" />
            Prerequisites
          </h3>
          <ul className="space-y-2">
            {project.prerequisites.map((prereq, index) => (
              <li key={index} className="flex items-center text-gray-300">
                <CheckCircle className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                {prereq}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
