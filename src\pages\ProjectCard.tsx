import React from "react";
import { Link } from "react-router-dom";
import { Star, GitBranch } from "lucide-react";

interface DocsCardProps {
  id: string;
  title: string;
  description: string;
  image: string;
  stars: number;
  forks: number;
  tags: string[];
}
export function DocsCard({
  id,
  title,
  description,
  image,
  stars,
  forks,
  tags,
}: DocsCardProps) {
  return (
    <Link to={`/documentation/${id}`} className="block">
      <div className="bg-gray-800 rounded-lg overflow-hidden hover:ring-2 hover:ring-blue-500 transition-all">
        <img src={image} alt={title} className="w-full h-48 object-cover" />
        <div className="p-4">
          <h3 className="text-lg font-semibold text-gray-100">{title}</h3>
          <p className="mt-2 text-sm text-gray-400">{description}</p>
          <div className="mt-4 flex items-center justify-between">
            <div className="flex space-x-4">
              <div className="flex items-center text-gray-400">
                <Star className="h-4 w-4 mr-1" />
                <span className="text-sm">{stars}</span>
              </div>
              <div className="flex items-center text-gray-400">
                <GitBranch className="h-4 w-4 mr-1" />
                <span className="text-sm">{forks}</span>
              </div>
            </div>
            <div className="flex space-x-2">
              {tags.map((tag) => (
                <span
                  key={tag}
                  className="px-2 py-1 text-xs rounded-full bg-gray-700 text-gray-300"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
