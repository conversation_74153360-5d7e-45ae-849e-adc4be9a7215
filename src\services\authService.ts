import { User, GitHubUser, LoginResponse, POINTS_SYSTEM, calculateLevel } from '../types/userTypes';

// GitHub OAuth configuration
const GITHUB_CLIENT_ID = import.meta.env.VITE_GITHUB_CLIENT_ID || 'demo_client_id';
const GITHUB_REDIRECT_URI = import.meta.env.VITE_GITHUB_REDIRECT_URI || `${window.location.origin}/auth/callback`;
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

class AuthService {
  private baseUrl = API_BASE_URL;

  // GitHub OAuth flow
  initiateGitHubLogin(): string {
    const params = new URLSearchParams({
      client_id: GITHUB_CLIENT_ID,
      redirect_uri: GITHUB_REDIRECT_URI,
      scope: 'user:email read:user',
      state: this.generateState(),
    });

    const authUrl = `https://github.com/login/oauth/authorize?${params.toString()}`;
    return authUrl;
  }

  async loginWithGitHub(code: string): Promise<LoginResponse> {
    try {
      // In a real app, this would call your backend API
      // For demo purposes, we'll simulate the GitHub OAuth flow
      const githubUser = await this.getGitHubUserFromCode(code);
      const user = await this.createOrUpdateUser(githubUser);
      const token = this.generateToken(user);

      return {
        user,
        token,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      };
    } catch (error) {
      console.error('GitHub login failed:', error);
      throw new Error('Failed to authenticate with GitHub');
    }
  }

  async getCurrentUser(): Promise<User> {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    try {
      // In a real app, this would validate the token with your backend
      // For demo purposes, we'll return a mock user based on stored data
      const userData = localStorage.getItem('user_data');
      if (userData) {
        return JSON.parse(userData);
      }
      throw new Error('User data not found');
    } catch (error) {
      console.error('Failed to get current user:', error);
      throw new Error('Failed to get user information');
    }
  }

  async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    try {
      // In a real app, this would call your backend API
      const currentUser = await this.getCurrentUser();
      const updatedUser = { ...currentUser, ...updates };

      // Store updated user data
      localStorage.setItem('user_data', JSON.stringify(updatedUser));

      return updatedUser;
    } catch (error) {
      console.error('Failed to update user:', error);
      throw new Error('Failed to update user');
    }
  }

  logout(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    localStorage.removeItem('user_contributions');
    localStorage.removeItem('user_bookmarks');
    localStorage.removeItem('user_badges');
  }

  // Private helper methods
  private async getGitHubUserFromCode(code: string): Promise<GitHubUser> {
    // In a real app, this would exchange the code for an access token
    // and then fetch the user data from GitHub API
    // For demo purposes, we'll return mock data

    // Simulate different users based on code
    const mockUsers: GitHubUser[] = [
      {
        id: 12345,
        login: 'demo_user',
        name: 'Demo User',
        email: '<EMAIL>',
        avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        bio: 'Full-stack developer passionate about open source',
        location: 'San Francisco, CA',
        blog: 'https://demo-user.dev',
        html_url: 'https://github.com/demo_user',
        public_repos: 42,
        followers: 156,
        following: 89,
        created_at: '2020-01-15T00:00:00Z',
      },
      {
        id: 67890,
        login: 'jane_dev',
        name: 'Jane Developer',
        email: '<EMAIL>',
        avatar_url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        bio: 'Frontend engineer and UI/UX enthusiast',
        location: 'New York, NY',
        blog: 'https://jane-dev.com',
        html_url: 'https://github.com/jane_dev',
        public_repos: 28,
        followers: 234,
        following: 67,
        created_at: '2019-06-20T00:00:00Z',
      },
    ];

    // Return a random mock user for demo
    const randomUser = mockUsers[Math.floor(Math.random() * mockUsers.length)];
    return randomUser;
  }

  private async createOrUpdateUser(githubUser: GitHubUser): Promise<User> {
    const now = new Date().toISOString();

    // Check if user exists in localStorage
    const existingUserData = localStorage.getItem('user_data');
    let user: User;

    if (existingUserData) {
      const existingUser = JSON.parse(existingUserData);
      // Update existing user with latest GitHub data
      user = {
        ...existingUser,
        name: githubUser.name,
        email: githubUser.email,
        avatar: githubUser.avatar_url,
        bio: githubUser.bio,
        location: githubUser.location,
        website: githubUser.blog,
        lastActive: now,
      };
    } else {
      // Create new user
      user = {
        id: `user_${githubUser.id}`,
        githubId: githubUser.id,
        username: githubUser.login,
        name: githubUser.name,
        email: githubUser.email,
        avatar: githubUser.avatar_url,
        bio: githubUser.bio,
        location: githubUser.location,
        website: githubUser.blog,
        githubUrl: githubUser.html_url,
        joinedAt: now,
        lastActive: now,
        isPublic: true,
        stats: {
          totalContributions: 0,
          projectsViewed: 0,
          projectsBookmarked: 0,
          prsSubmitted: 0,
          issuesOpened: 0,
          commentsPosted: 0,
          streakDays: 0,
          longestStreak: 0,
          totalPoints: 0,
          level: 1,
        },
        preferences: {
          theme: 'dark',
          emailNotifications: true,
          publicProfile: true,
          showActivity: true,
          preferredLanguages: [],
          interestedDomains: [],
        },
      };
    }

    // Store user data
    localStorage.setItem('user_data', JSON.stringify(user));

    return user;
  }

  private generateToken(user: User): string {
    // In a real app, this would be a proper JWT token generated by your backend
    // For demo purposes, we'll create a simple token
    const payload = {
      userId: user.id,
      username: user.username,
      iat: Date.now(),
    };

    return btoa(JSON.stringify(payload));
  }

  private generateState(): string {
    return Math.random().toString(36).substring(2, 15) +
           Math.random().toString(36).substring(2, 15);
  }

  // Utility methods for demo
  async simulateGitHubPR(projectUrl: string, prTitle: string): Promise<{ url: string; status: string }> {
    // Simulate creating a PR through GitHub API
    const prNumber = Math.floor(Math.random() * 1000) + 1;
    const prUrl = `${projectUrl}/pull/${prNumber}`;

    return {
      url: prUrl,
      status: 'open',
    };
  }

  async simulateGitHubIssue(projectUrl: string, issueTitle: string): Promise<{ url: string; status: string }> {
    // Simulate creating an issue through GitHub API
    const issueNumber = Math.floor(Math.random() * 1000) + 1;
    const issueUrl = `${projectUrl}/issues/${issueNumber}`;

    return {
      url: issueUrl,
      status: 'open',
    };
  }
}

export const authService = new AuthService();
