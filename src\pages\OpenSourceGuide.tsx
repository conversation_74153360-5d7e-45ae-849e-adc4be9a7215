import React from "react";
import { 
  GitBranch, 
  Users, 
  Star, 
  GitFork, 
  MessageSquare, 
  CheckCircle, 
  AlertCircle, 
  BookOpen,
  Code,
  Heart,
  Search,
  ExternalLink
} from "lucide-react";

export function OpenSourceGuide() {
  return (
    <div className="max-w-6xl mx-auto space-y-12">
      {/* Hero Section */}
      <section className="text-center space-y-6">
        <div className="flex justify-center">
          <GitBranch className="h-16 w-16 text-blue-500" />
        </div>
        <h1 className="text-4xl font-bold">Open Source Contribution Guide</h1>
        <p className="text-xl text-gray-400 max-w-3xl mx-auto">
          Your complete guide to making your first open-source contribution. 
          Learn how to find projects, understand workflows, and become part of the global developer community.
        </p>
      </section>

      {/* Quick Start Steps */}
      <section className="bg-gray-800 rounded-lg p-8">
        <h2 className="text-2xl font-bold mb-6 flex items-center">
          <CheckCircle className="h-6 w-6 text-green-500 mr-3" />
          Quick Start: Your First Contribution in 5 Steps
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          {[
            { step: 1, title: "Find a Project", icon: Search, desc: "Look for beginner-friendly projects" },
            { step: 2, title: "Fork & Clone", icon: GitFork, desc: "Create your own copy" },
            { step: 3, title: "Make Changes", icon: Code, desc: "Fix bugs or add features" },
            { step: 4, title: "Submit PR", icon: GitBranch, desc: "Propose your changes" },
            { step: 5, title: "Collaborate", icon: Users, desc: "Work with maintainers" }
          ].map(({ step, title, icon: Icon, desc }) => (
            <div key={step} className="text-center">
              <div className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-3 text-sm font-bold">
                {step}
              </div>
              <Icon className="h-8 w-8 text-blue-400 mx-auto mb-2" />
              <h3 className="font-semibold mb-1">{title}</h3>
              <p className="text-sm text-gray-400">{desc}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Finding Projects Section */}
      <section>
        <h2 className="text-3xl font-bold mb-6">Finding the Right Project</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Star className="h-5 w-5 text-yellow-500 mr-2" />
                Beginner-Friendly Labels
              </h3>
              <ul className="space-y-2">
                {[
                  "good first issue",
                  "beginner-friendly",
                  "help wanted",
                  "easy",
                  "newcomer",
                  "first-timers-only"
                ].map((label) => (
                  <li key={label} className="flex items-center">
                    <span className="bg-green-600 text-white px-2 py-1 rounded text-sm mr-3">
                      {label}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Heart className="h-5 w-5 text-red-500 mr-2" />
                Choose Projects You Use
              </h3>
              <p className="text-gray-400 mb-4">
                Start with tools, libraries, or frameworks you already use. You'll have better context and motivation.
              </p>
              <ul className="text-gray-400 space-y-1">
                <li>• Your favorite VS Code extensions</li>
                <li>• JavaScript/Python libraries you use</li>
                <li>• Documentation improvements</li>
                <li>• Translation projects</li>
              </ul>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Popular Platforms</h3>
              <div className="space-y-4">
                {[
                  { name: "GitHub", desc: "Largest code hosting platform", url: "https://github.com" },
                  { name: "GitLab", desc: "DevOps platform with built-in CI/CD", url: "https://gitlab.com" },
                  { name: "Codeberg", desc: "Non-profit Git hosting", url: "https://codeberg.org" },
                  { name: "SourceForge", desc: "One of the oldest platforms", url: "https://sourceforge.net" }
                ].map(({ name, desc, url }) => (
                  <a
                    key={name}
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block bg-gray-700 hover:bg-gray-600 rounded-lg p-4 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{name}</h4>
                        <p className="text-sm text-gray-400">{desc}</p>
                      </div>
                      <ExternalLink className="h-4 w-4 text-gray-400" />
                    </div>
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contribution Workflow */}
      <section>
        <h2 className="text-3xl font-bold mb-6">Contribution Workflow</h2>
        <div className="bg-gray-800 rounded-lg p-8">
          <div className="space-y-8">
            {[
              {
                title: "1. Fork the Repository",
                desc: "Create your own copy of the project",
                code: "# Click 'Fork' button on GitHub\n# Then clone your fork\ngit clone https://github.com/yourusername/project-name.git"
              },
              {
                title: "2. Create a Branch",
                desc: "Always work on a separate branch for your changes",
                code: "git checkout -b feature/your-feature-name\n# or\ngit checkout -b fix/issue-number"
              },
              {
                title: "3. Make Your Changes",
                desc: "Write clean, well-documented code",
                code: "# Make your changes\n# Test your changes\nnpm test  # or appropriate test command\n# Add and commit\ngit add .\ngit commit -m \"Add: descriptive commit message\""
              },
              {
                title: "4. Push and Create Pull Request",
                desc: "Share your changes with the project maintainers",
                code: "git push origin feature/your-feature-name\n# Then create a Pull Request on GitHub"
              }
            ].map(({ title, desc, code }) => (
              <div key={title} className="border-l-4 border-blue-500 pl-6">
                <h3 className="text-xl font-semibold mb-2">{title}</h3>
                <p className="text-gray-400 mb-4">{desc}</p>
                <pre className="bg-gray-900 rounded p-4 text-sm overflow-x-auto">
                  <code className="text-green-400">{code}</code>
                </pre>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Best Practices */}
      <section>
        <h2 className="text-3xl font-bold mb-6">Best Practices</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center text-green-400">
                <CheckCircle className="h-5 w-5 mr-2" />
                Do's
              </h3>
              <ul className="space-y-2 text-gray-300">
                <li>• Read the project's CONTRIBUTING.md file</li>
                <li>• Follow the existing code style</li>
                <li>• Write clear commit messages</li>
                <li>• Test your changes thoroughly</li>
                <li>• Be patient and respectful</li>
                <li>• Ask questions if you're unsure</li>
                <li>• Start small with documentation fixes</li>
              </ul>
            </div>
          </div>
          
          <div className="space-y-6">
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center text-red-400">
                <AlertCircle className="h-5 w-5 mr-2" />
                Don'ts
              </h3>
              <ul className="space-y-2 text-gray-300">
                <li>• Don't submit massive changes without discussion</li>
                <li>• Don't ignore project guidelines</li>
                <li>• Don't take feedback personally</li>
                <li>• Don't work on main/master branch</li>
                <li>• Don't submit untested code</li>
                <li>• Don't be discouraged by rejection</li>
                <li>• Don't spam maintainers</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Community Guidelines */}
      <section>
        <h2 className="text-3xl font-bold mb-6">Community Guidelines</h2>
        <div className="bg-gray-800 rounded-lg p-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <Users className="h-12 w-12 text-blue-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Be Respectful</h3>
              <p className="text-gray-400">
                Treat everyone with respect, regardless of their experience level or background.
              </p>
            </div>
            <div className="text-center">
              <MessageSquare className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Communicate Clearly</h3>
              <p className="text-gray-400">
                Use clear, concise language. Provide context and examples when asking questions.
              </p>
            </div>
            <div className="text-center">
              <BookOpen className="h-12 w-12 text-purple-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Keep Learning</h3>
              <p className="text-gray-400">
                Every contribution is a learning opportunity. Embrace feedback and grow.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Resources */}
      <section>
        <h2 className="text-3xl font-bold mb-6">Helpful Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            {
              title: "First Contributions",
              desc: "Hands-on tutorial for making your first contribution",
              url: "https://firstcontributions.github.io/",
              category: "Tutorial"
            },
            {
              title: "Good First Issues",
              desc: "Curated list of beginner-friendly issues",
              url: "https://goodfirstissues.com/",
              category: "Issues"
            },
            {
              title: "Up For Grabs",
              desc: "Projects with tasks for new contributors",
              url: "https://up-for-grabs.net/",
              category: "Projects"
            },
            {
              title: "CodeTriage",
              desc: "Get issues delivered to your inbox",
              url: "https://www.codetriage.com/",
              category: "Platform"
            },
            {
              title: "Hacktoberfest",
              desc: "Annual event encouraging open source contributions",
              url: "https://hacktoberfest.digitalocean.com/",
              category: "Event"
            },
            {
              title: "Open Source Guide",
              desc: "GitHub's comprehensive guide to open source",
              url: "https://opensource.guide/",
              category: "Guide"
            }
          ].map(({ title, desc, url, category }) => (
            <a
              key={title}
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className="block bg-gray-800 hover:bg-gray-700 rounded-lg p-6 transition-colors"
            >
              <div className="flex items-start justify-between mb-3">
                <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs">
                  {category}
                </span>
                <ExternalLink className="h-4 w-4 text-gray-400" />
              </div>
              <h3 className="font-semibold mb-2">{title}</h3>
              <p className="text-sm text-gray-400">{desc}</p>
            </a>
          ))}
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Ready to Make Your First Contribution?</h2>
        <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
          The open source community is waiting for your unique perspective and skills. 
          Start small, be consistent, and watch your impact grow.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="https://github.com/explore"
            target="_blank"
            rel="noopener noreferrer"
            className="bg-white text-blue-600 hover:bg-gray-100 px-6 py-3 rounded-lg font-medium inline-flex items-center justify-center"
          >
            Explore Projects <ExternalLink className="ml-2 h-4 w-4" />
          </a>
          <a
            href="https://firstcontributions.github.io/"
            target="_blank"
            rel="noopener noreferrer"
            className="bg-blue-800 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center justify-center"
          >
            Start Tutorial <BookOpen className="ml-2 h-4 w-4" />
          </a>
        </div>
      </section>
    </div>
  );
}
