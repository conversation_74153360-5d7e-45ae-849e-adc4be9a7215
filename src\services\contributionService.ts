import {
  Contribution,
  UserStats,
  Badge,
  UserBadge,
  BookmarkedProject,
  POINTS_SYSTEM,
  BADGE_DEFINITIONS,
  calculateLevel,
  ActivityFeed
} from '../types/userTypes';
import { PROJECTS_DATA } from '../constants/projects.data';

class ContributionService {
  async trackContribution(contribution: Omit<Contribution, 'id' | 'timestamp'>): Promise<Contribution> {
    const now = new Date().toISOString();
    const newContribution: Contribution = {
      ...contribution,
      id: `contrib_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: now,
    };

    // Get project title if not provided
    if (!newContribution.projectTitle && newContribution.projectId) {
      const project = PROJECTS_DATA.find(p => p.id === newContribution.projectId);
      newContribution.projectTitle = project?.title || 'Unknown Project';
      newContribution.projectUrl = project?.repositoryUrl;
    }

    // Store contribution
    const contributions = this.getStoredContributions(contribution.userId);
    contributions.push(newContribution);
    localStorage.setItem(`contributions_${contribution.userId}`, JSON.stringify(contributions));

    // Update user stats
    await this.updateUserStats(contribution.userId, newContribution);

    // Check for new badges
    await this.checkAndAwardBadges(contribution.userId);

    return newContribution;
  }

  async getUserContributions(userId: string, limit?: number): Promise<Contribution[]> {
    const contributions = this.getStoredContributions(userId);

    // Sort by timestamp (newest first)
    contributions.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    return limit ? contributions.slice(0, limit) : contributions;
  }

  async getUserStats(userId: string): Promise<UserStats> {
    const contributions = this.getStoredContributions(userId);
    const badges = this.getStoredUserBadges(userId);

    const stats: UserStats = {
      totalContributions: contributions.length,
      projectsViewed: contributions.filter(c => c.type === 'view').length,
      projectsBookmarked: contributions.filter(c => c.type === 'bookmark').length,
      prsSubmitted: contributions.filter(c => c.type === 'pr').length,
      issuesOpened: contributions.filter(c => c.type === 'issue').length,
      commentsPosted: contributions.filter(c => c.type === 'comment').length,
      streakDays: this.calculateCurrentStreak(contributions),
      longestStreak: this.calculateLongestStreak(contributions),
      totalPoints: contributions.reduce((sum, c) => sum + c.points, 0),
      level: 1,
    };

    stats.level = calculateLevel(stats.totalPoints);

    return stats;
  }

  async bookmarkProject(userId: string, projectId: string, notes?: string): Promise<BookmarkedProject> {
    const bookmark: BookmarkedProject = {
      id: `bookmark_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      projectId,
      bookmarkedAt: new Date().toISOString(),
      notes,
    };

    const bookmarks = this.getStoredBookmarks(userId);

    // Check if already bookmarked
    const existingIndex = bookmarks.findIndex(b => b.projectId === projectId);
    if (existingIndex >= 0) {
      // Update existing bookmark
      bookmarks[existingIndex] = { ...bookmarks[existingIndex], ...bookmark };
    } else {
      // Add new bookmark
      bookmarks.push(bookmark);
    }

    localStorage.setItem(`bookmarks_${userId}`, JSON.stringify(bookmarks));
    return bookmark;
  }

  async unbookmarkProject(userId: string, projectId: string): Promise<void> {
    const bookmarks = this.getStoredBookmarks(userId);
    const filteredBookmarks = bookmarks.filter(b => b.projectId !== projectId);
    localStorage.setItem(`bookmarks_${userId}`, JSON.stringify(filteredBookmarks));
  }

  async getBookmarkedProjects(userId: string): Promise<BookmarkedProject[]> {
    return this.getStoredBookmarks(userId);
  }

  async getUserBadges(userId: string): Promise<{ badges: Badge[], userBadges: UserBadge[] }> {
    const userBadges = this.getStoredUserBadges(userId);
    const earnedBadgeIds = userBadges.map(ub => ub.badgeId);
    const earnedBadges = BADGE_DEFINITIONS.filter(b => earnedBadgeIds.includes(b.id));

    return {
      badges: earnedBadges,
      userBadges,
    };
  }

  async getAvailableBadges(userId: string): Promise<Badge[]> {
    const userBadges = this.getStoredUserBadges(userId);
    const earnedBadgeIds = userBadges.map(ub => ub.badgeId);

    return BADGE_DEFINITIONS.filter(b => !earnedBadgeIds.includes(b.id));
  }

  async getActivityFeed(userId: string, limit: number = 20): Promise<ActivityFeed[]> {
    const activities = this.getStoredActivityFeed(userId);

    // Sort by timestamp (newest first)
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    return activities.slice(0, limit);
  }

  // Private helper methods
  private getStoredContributions(userId: string): Contribution[] {
    const stored = localStorage.getItem(`contributions_${userId}`);
    return stored ? JSON.parse(stored) : [];
  }

  private getStoredBookmarks(userId: string): BookmarkedProject[] {
    const stored = localStorage.getItem(`bookmarks_${userId}`);
    return stored ? JSON.parse(stored) : [];
  }

  private getStoredUserBadges(userId: string): UserBadge[] {
    const stored = localStorage.getItem(`badges_${userId}`);
    return stored ? JSON.parse(stored) : [];
  }

  private getStoredActivityFeed(userId: string): ActivityFeed[] {
    const stored = localStorage.getItem(`activity_${userId}`);
    return stored ? JSON.parse(stored) : [];
  }

  private async updateUserStats(userId: string, contribution: Contribution): Promise<void> {
    // Update user data in localStorage
    const userData = localStorage.getItem('user_data');
    if (userData) {
      const user = JSON.parse(userData);
      const updatedStats = await this.getUserStats(userId);
      user.stats = updatedStats;
      localStorage.setItem('user_data', JSON.stringify(user));
    }
  }

  private async checkAndAwardBadges(userId: string): Promise<void> {
    const stats = await this.getUserStats(userId);
    const userBadges = this.getStoredUserBadges(userId);
    const earnedBadgeIds = userBadges.map(ub => ub.badgeId);

    const newBadges: UserBadge[] = [];

    for (const badge of BADGE_DEFINITIONS) {
      if (earnedBadgeIds.includes(badge.id)) continue;

      let earned = false;

      switch (badge.requirement.type) {
        case 'count':
          const value = this.getStatValue(stats, badge.requirement.metric);
          earned = value >= badge.requirement.target;
          break;
        case 'streak':
          earned = stats.streakDays >= badge.requirement.target;
          break;
        case 'special':
          // Special badges are awarded manually or through specific events
          break;
      }

      if (earned) {
        const userBadge: UserBadge = {
          badgeId: badge.id,
          userId,
          unlockedAt: new Date().toISOString(),
        };

        newBadges.push(userBadge);

        // Add to activity feed
        this.addToActivityFeed(userId, {
          type: 'badge_earned',
          title: `Badge Earned: ${badge.name}`,
          description: badge.description,
          metadata: { badgeId: badge.id },
        });
      }
    }

    if (newBadges.length > 0) {
      const allUserBadges = [...userBadges, ...newBadges];
      localStorage.setItem(`badges_${userId}`, JSON.stringify(allUserBadges));
    }
  }

  private getStatValue(stats: UserStats, metric: string): number {
    switch (metric) {
      case 'prs': return stats.prsSubmitted;
      case 'views': return stats.projectsViewed;
      case 'bookmarks': return stats.projectsBookmarked;
      case 'points': return stats.totalPoints;
      case 'projects': return stats.projectsViewed; // or unique projects
      default: return 0;
    }
  }

  private calculateCurrentStreak(contributions: Contribution[]): number {
    if (contributions.length === 0) return 0;

    // Group contributions by date
    const dateGroups = new Map<string, Contribution[]>();
    contributions.forEach(c => {
      const date = new Date(c.timestamp).toDateString();
      if (!dateGroups.has(date)) {
        dateGroups.set(date, []);
      }
      dateGroups.get(date)!.push(c);
    });

    const sortedDates = Array.from(dateGroups.keys()).sort((a, b) =>
      new Date(b).getTime() - new Date(a).getTime()
    );

    let streak = 0;
    let currentDate = new Date();

    for (const dateStr of sortedDates) {
      const date = new Date(dateStr);
      const daysDiff = Math.floor((currentDate.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff === streak) {
        streak++;
      } else if (daysDiff === streak + 1) {
        // Allow for one day gap (today might not have contributions yet)
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  private calculateLongestStreak(contributions: Contribution[]): number {
    // Similar to current streak but find the longest historical streak
    // For simplicity, we'll return current streak for now
    return this.calculateCurrentStreak(contributions);
  }

  private addToActivityFeed(userId: string, activity: Omit<ActivityFeed, 'id' | 'userId' | 'timestamp' | 'isPublic'>): void {
    const activities = this.getStoredActivityFeed(userId);

    const newActivity: ActivityFeed = {
      ...activity,
      id: `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      timestamp: new Date().toISOString(),
      isPublic: true,
    };

    activities.push(newActivity);

    // Keep only last 100 activities
    if (activities.length > 100) {
      activities.splice(0, activities.length - 100);
    }

    localStorage.setItem(`activity_${userId}`, JSON.stringify(activities));
  }
}

export const contributionService = new ContributionService();
