import React from "react";
import { ArrowRight } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { ProjectCard } from "../components/features/projects/projectCard";
import { featuredProjects } from "../utils/featuredProjects";

export function Home() {
  return (
    <div className="space-y-12">
      <section className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Welcome to IDXCodeHub</h1>
        <p className="text-xl text-gray-400 max-w-2xl mx-auto">
          Discover, share, and collaborate on amazing open-source projects
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link
            to="/projects"
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg font-medium flex items-center justify-center"
          >
            Explore Projects <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
          <Link
            to="/open-source-guide"
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium flex items-center justify-center"
          >
            Start Contributing <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
          <Link
            to="/documentation"
            className="bg-gray-800 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium"
          >
            View Docs
          </Link>
        </div>
      </section>
      <section>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Featured Projects</h2>
          <Link
            to="/projects"
            className="text-blue-500 hover:text-blue-400 flex items-center"
          >
            View all <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredProjects.map((project) => (
            <ProjectCard key={project.id} {...project} />
          ))}
        </div>
      </section>
      <section className="bg-gradient-to-r from-green-600 to-blue-600 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Ready to Make Your First Contribution?</h2>
        <p className="text-green-100 mb-6 max-w-2xl mx-auto">
          New to open source? Our comprehensive guide will walk you through everything you need to know
          to make your first contribution with confidence.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/open-source-guide"
            className="bg-white text-green-600 hover:bg-gray-100 px-6 py-2 rounded-lg font-medium inline-flex items-center justify-center"
          >
            Read the Guide <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
          <a
            href="https://github.com/explore"
            target="_blank"
            rel="noopener noreferrer"
            className="bg-green-800 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium inline-flex items-center justify-center"
          >
            Explore Projects <ArrowRight className="ml-2 h-4 w-4" />
          </a>
        </div>
      </section>
    </div>
  );
}
