import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Loader2, CheckCircle, XCircle, ArrowRight } from 'lucide-react';

export function AuthCallback() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { login } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const handleCallback = async () => {
      const code = searchParams.get('code');
      const error = searchParams.get('error');
      const state = searchParams.get('state');

      if (error) {
        setStatus('error');
        setError(`GitHub OAuth error: ${error}`);
        return;
      }

      if (!code) {
        setStatus('error');
        setError('No authorization code received from GitHub');
        return;
      }

      try {
        await login(code);
        setStatus('success');
        
        // Redirect to profile page after successful login
        setTimeout(() => {
          navigate('/profile', { replace: true });
        }, 2000);
      } catch (err) {
        setStatus('error');
        setError(err instanceof Error ? err.message : 'Login failed');
      }
    };

    handleCallback();
  }, [searchParams, login, navigate]);

  const handleRetry = () => {
    navigate('/projects', { replace: true });
  };

  const handleGoToProfile = () => {
    navigate('/profile', { replace: true });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="max-w-md w-full mx-4">
        <div className="bg-gray-800 rounded-lg p-8 text-center border border-gray-700">
          {status === 'loading' && (
            <>
              <Loader2 className="h-12 w-12 text-blue-500 mx-auto mb-4 animate-spin" />
              <h2 className="text-xl font-semibold text-gray-200 mb-2">
                Authenticating with GitHub
              </h2>
              <p className="text-gray-400">
                Please wait while we complete your login...
              </p>
            </>
          )}

          {status === 'success' && (
            <>
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-200 mb-2">
                Welcome to IDXCodeHub!
              </h2>
              <p className="text-gray-400 mb-6">
                Your GitHub account has been successfully connected. You'll be redirected to your profile shortly.
              </p>
              <button
                onClick={handleGoToProfile}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center transition-colors"
              >
                Go to Profile <ArrowRight className="ml-2 h-4 w-4" />
              </button>
            </>
          )}

          {status === 'error' && (
            <>
              <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-200 mb-2">
                Authentication Failed
              </h2>
              <p className="text-gray-400 mb-4">
                {error}
              </p>
              <div className="space-y-3">
                <button
                  onClick={handleRetry}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  Try Again
                </button>
                <button
                  onClick={() => navigate('/', { replace: true })}
                  className="w-full bg-gray-700 hover:bg-gray-600 text-gray-300 px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  Go Home
                </button>
              </div>
            </>
          )}
        </div>

        {/* Additional Info */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            By signing in, you agree to our{' '}
            <a href="/terms" className="text-blue-400 hover:text-blue-300">
              Terms of Service
            </a>{' '}
            and{' '}
            <a href="/privacy" className="text-blue-400 hover:text-blue-300">
              Privacy Policy
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
