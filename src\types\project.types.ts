// Project-related type definitions
export interface Project {
  id: string;
  title: string;
  description: string;
  image: string;
  stars: number;
  forks: number;
  tags: string[];
  language: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  domain: string;
  activityLevel: 'Low' | 'Medium' | 'High';
  lastUpdated: string;
  contributors: number;
  issues: number;
  license: string;
  keywords: string[];
  
  // Extended project information
  longDescription?: string;
  repositoryUrl?: string;
  demoUrl?: string;
  documentationUrl?: string;
  technologies?: string[];
  prerequisites?: string[];
  setupInstructions?: string;
  contributionGuide?: string;
  goodFirstIssues?: Issue[];
  maintainers?: Maintainer[];
  recentCommits?: Commit[];
  projectStructure?: string;
  readme?: string;
}

export interface Issue {
  id: string;
  title: string;
  description: string;
  labels: string[];
  difficulty: 'Easy' | 'Medium' | 'Hard';
  estimatedTime: string;
  url: string;
  createdAt: string;
  author: string;
}

export interface Maintainer {
  id: string;
  name: string;
  avatar: string;
  role: string;
  githubUrl: string;
  contributions: number;
}

export interface Commit {
  id: string;
  message: string;
  author: string;
  date: string;
  url: string;
}

// Search and filter types
export interface SearchFilters {
  query: string;
  languages: string[];
  difficulties: string[];
  domains: string[];
  activityLevels: string[];
  sortBy: 'stars' | 'forks' | 'updated' | 'name';
  sortOrder: 'asc' | 'desc';
}

export interface SearchResult {
  projects: Project[];
  totalCount: number;
  hasMore: boolean;
  filters: SearchFilters;
}

// Project statistics
export interface ProjectStats {
  totalProjects: number;
  languageDistribution: Record<string, number>;
  difficultyDistribution: Record<string, number>;
  domainDistribution: Record<string, number>;
  averageStars: number;
  totalStars: number;
  totalForks: number;
}
