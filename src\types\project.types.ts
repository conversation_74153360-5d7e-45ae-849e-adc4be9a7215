// Project-related type definitions
export interface Project {
  id: string;
  title: string;
  description: string;
  image: string;
  stars: number;
  forks: number;
  tags: string[];
  language: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  domain: string;
  activityLevel: 'Low' | 'Medium' | 'High';
  lastUpdated: string;
  contributors: number;
  issues: number;
  license: string;
  keywords: string[];

  // Extended project information
  longDescription?: string;
  repositoryUrl?: string;
  demoUrl?: string;
  documentationUrl?: string;
  technologies?: string[];
  prerequisites?: string[];
  setupInstructions?: string;
  contributionGuide?: string;
  goodFirstIssues?: Issue[];
  maintainers?: Maintainer[];
  recentCommits?: Commit[];
  projectStructure?: string;
  readme?: string;

  // Data source tracking
  source: 'github' | 'upforgrabs' | 'codetriage' | 'static';
  sourceId: string; // Original ID from the source
  fetchedAt: string; // When this data was fetched
  isActive: boolean; // Whether the project is still active
}

export interface Issue {
  id: string;
  title: string;
  description: string;
  labels: string[];
  difficulty: 'Easy' | 'Medium' | 'Hard';
  estimatedTime: string;
  url: string;
  createdAt: string;
  author: string;
}

export interface Maintainer {
  id: string;
  name: string;
  avatar: string;
  role: string;
  githubUrl: string;
  contributions: number;
}

export interface Commit {
  id: string;
  message: string;
  author: string;
  date: string;
  url: string;
}

// GitHub API Response Types
export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  html_url: string;
  clone_url: string;
  stargazers_count: number;
  forks_count: number;
  language: string | null;
  topics: string[];
  license: {
    key: string;
    name: string;
  } | null;
  updated_at: string;
  created_at: string;
  pushed_at: string;
  open_issues_count: number;
  subscribers_count: number;
  owner: {
    login: string;
    avatar_url: string;
    html_url: string;
  };
  default_branch: string;
  has_issues: boolean;
  has_wiki: boolean;
  archived: boolean;
  disabled: boolean;
}

export interface GitHubIssue {
  id: number;
  number: number;
  title: string;
  body: string | null;
  html_url: string;
  state: 'open' | 'closed';
  labels: Array<{
    name: string;
    color: string;
    description: string | null;
  }>;
  created_at: string;
  updated_at: string;
  user: {
    login: string;
    avatar_url: string;
  };
}

// Up For Grabs API Types
export interface UpForGrabsProject {
  name: string;
  desc: string;
  site: string;
  tags: string[];
  upforgrabs: {
    name: string;
    link: string;
  };
  stats: {
    issue_count: number;
    last_updated: string;
  };
}

// Project Search and Filter Types
export interface ProjectFilters {
  query?: string;
  languages?: string[];
  difficulties?: Array<'Beginner' | 'Intermediate' | 'Advanced'>;
  domains?: string[];
  activityLevels?: Array<'Low' | 'Medium' | 'High'>;
  minStars?: number;
  maxStars?: number;
  hasGoodFirstIssues?: boolean;
  sources?: Array<'github' | 'upforgrabs' | 'codetriage' | 'static'>;
}

export interface ProjectSearchResult {
  projects: Project[];
  totalCount: number;
  hasMore: boolean;
  nextCursor?: string;
}

// API Response wrapper
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

// Search and filter types
export interface SearchFilters {
  query: string;
  languages: string[];
  difficulties: string[];
  domains: string[];
  activityLevels: string[];
  sortBy: 'stars' | 'forks' | 'updated' | 'name';
  sortOrder: 'asc' | 'desc';
}

export interface SearchResult {
  projects: Project[];
  totalCount: number;
  hasMore: boolean;
  filters: SearchFilters;
}

// Project statistics
export interface ProjectStats {
  totalProjects: number;
  languageDistribution: Record<string, number>;
  difficultyDistribution: Record<string, number>;
  domainDistribution: Record<string, number>;
  averageStars: number;
  totalStars: number;
  totalForks: number;
}
