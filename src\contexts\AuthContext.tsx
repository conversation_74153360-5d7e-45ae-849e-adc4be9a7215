import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { User, AuthState, Contribution, Badge, UserBadge, BookmarkedProject } from '../types/userTypes';
import { authService } from '../services/authService';
import { contributionService } from '../services/contributionService';
import { initializeDemoData } from '../utils/demoData';

interface AuthContextType extends AuthState {
  login: (code: string) => Promise<void>;
  logout: () => void;
  updateUser: (updates: Partial<User>) => Promise<void>;
  trackContribution: (contribution: Omit<Contribution, 'id' | 'userId' | 'timestamp'>) => Promise<void>;
  bookmarkProject: (projectId: string, notes?: string) => Promise<void>;
  unbookmarkProject: (projectId: string) => Promise<void>;
  getUserContributions: () => Promise<Contribution[]>;
  getUserBadges: () => Promise<{ badges: Badge[], userBadges: UserBadge[] }>;
  getBookmarkedProjects: () => Promise<BookmarkedProject[]>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: Partial<User> }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'CLEAR_ERROR' };

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  loading: true,
  error: null,
};

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, loading: true, error: null };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload,
        loading: false,
        error: null,
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        loading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        loading: false,
        error: null,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize auth state on app load
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Initialize demo data for testing
        initializeDemoData();

        const token = localStorage.getItem('auth_token');
        if (token) {
          const user = await authService.getCurrentUser();
          dispatch({ type: 'LOGIN_SUCCESS', payload: user });
        } else {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error);
        localStorage.removeItem('auth_token');
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    initializeAuth();
  }, []);

  const login = async (code: string) => {
    try {
      dispatch({ type: 'LOGIN_START' });
      const { user, token } = await authService.loginWithGitHub(code);
      localStorage.setItem('auth_token', token);
      dispatch({ type: 'LOGIN_SUCCESS', payload: user });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed';
      dispatch({ type: 'LOGIN_FAILURE', payload: message });
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('auth_token');
    authService.logout();
    dispatch({ type: 'LOGOUT' });
  };

  const updateUser = async (updates: Partial<User>) => {
    try {
      if (!state.user) throw new Error('No user logged in');
      const updatedUser = await authService.updateUser(state.user.id, updates);
      dispatch({ type: 'UPDATE_USER', payload: updatedUser });
    } catch (error) {
      console.error('Failed to update user:', error);
      throw error;
    }
  };

  const trackContribution = async (contribution: Omit<Contribution, 'id' | 'userId' | 'timestamp'>): Promise<void> => {
    try {
      if (!state.user) return;

      await contributionService.trackContribution({
        ...contribution,
        userId: state.user.id,
      });

      // Update user stats
      const updatedStats = await contributionService.getUserStats(state.user.id);
      dispatch({ type: 'UPDATE_USER', payload: { stats: updatedStats } });
    } catch (error) {
      console.error('Failed to track contribution:', error);
    }
  };

  const bookmarkProject = async (projectId: string, notes?: string) => {
    try {
      if (!state.user) throw new Error('No user logged in');
      await contributionService.bookmarkProject(state.user.id, projectId, notes);

      // Track as contribution
      await trackContribution({
        type: 'bookmark',
        projectId,
        projectTitle: '', // Will be filled by service
        points: 5,
      });
    } catch (error) {
      console.error('Failed to bookmark project:', error);
      throw error;
    }
  };

  const unbookmarkProject = async (projectId: string) => {
    try {
      if (!state.user) throw new Error('No user logged in');
      await contributionService.unbookmarkProject(state.user.id, projectId);
    } catch (error) {
      console.error('Failed to unbookmark project:', error);
      throw error;
    }
  };

  const getUserContributions = async (): Promise<Contribution[]> => {
    try {
      if (!state.user) return [];
      return await contributionService.getUserContributions(state.user.id);
    } catch (error) {
      console.error('Failed to get user contributions:', error);
      return [];
    }
  };

  const getUserBadges = async () => {
    try {
      if (!state.user) return { badges: [], userBadges: [] };
      return await contributionService.getUserBadges(state.user.id);
    } catch (error) {
      console.error('Failed to get user badges:', error);
      return { badges: [], userBadges: [] };
    }
  };

  const getBookmarkedProjects = async (): Promise<BookmarkedProject[]> => {
    try {
      if (!state.user) return [];
      return await contributionService.getBookmarkedProjects(state.user.id);
    } catch (error) {
      console.error('Failed to get bookmarked projects:', error);
      return [];
    }
  };

  const refreshUser = async () => {
    try {
      if (!state.user) return;
      const user = await authService.getCurrentUser();
      dispatch({ type: 'UPDATE_USER', payload: user });
    } catch (error) {
      console.error('Failed to refresh user:', error);
    }
  };

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    updateUser,
    trackContribution,
    bookmarkProject,
    unbookmarkProject,
    getUserContributions,
    getUserBadges,
    getBookmarkedProjects,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook for tracking project views
export function useProjectTracking() {
  const { trackContribution, user } = useAuth();

  const trackProjectView = async (projectId: string, projectTitle: string) => {
    if (!user) return;

    await trackContribution({
      type: 'view',
      projectId,
      projectTitle,
      points: 1,
    });
  };

  const trackProjectStar = async (projectId: string, projectTitle: string) => {
    if (!user) return;

    await trackContribution({
      type: 'star',
      projectId,
      projectTitle,
      points: 3,
    });
  };

  return {
    trackProjectView,
    trackProjectStar,
  };
}
