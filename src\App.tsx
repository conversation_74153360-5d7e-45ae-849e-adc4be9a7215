
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { Layout } from "./components/layout/Layout";
import { Home } from "./pages/Home";
import { Projects } from "./pages/Projects";
import { DocsCard } from "./pages/ProjectCard";
import { ProjectDetails } from "./pages/ProjectDetails";
import { Profile } from "./pages/Profile";
import { Documentation } from "./pages/Documentation";
import { OpenSourceGuide } from "./pages/OpenSourceGuide";
import { AuthCallback } from "./pages/AuthCallback";
import  Errorpage  from "./pages/Errorpage";
import { AuthProvider } from "./contexts/AuthContext";



const App = () => {
  return (
    <AuthProvider>
      <Router>
        <Layout>
          <Routes>
            <Route path="/"  element={<Home />} />
            <Route path="/projects" element={<Projects />} />
            <Route path="/documentation" element={<Documentation />} />
            <Route path="/open-source-guide" element={<OpenSourceGuide />} />
            <Route path="/documentation" element={<DocsCard id={"1"} title={""} description={""} image={""} stars={5} forks={0} tags={[]} />} />
            <Route path="/project/:id" element={<ProjectDetails />} />
            <Route path="/profile" element={<Profile />} />
            <Route path="/auth/callback" element={<AuthCallback />} />
            <Route path = "/*" element = {<Errorpage />} />
          </Routes>
        </Layout>
      </Router>
    </AuthProvider>
  )
}

export default App
