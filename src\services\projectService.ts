import { Project, ProjectFilters, ProjectSearchResult, ApiResponse } from '@/types/project.types';
import { githubService } from './githubService';
import { PROJECTS_DATA } from '@/constants/projects.data';

// Cache configuration
const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes
const CACHE_KEY_PREFIX = 'idxcodehub_projects_';

interface CachedData<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

class ProjectService {
  private cache = new Map<string, CachedData<any>>();

  private getCacheKey(key: string): string {
    return `${CACHE_KEY_PREFIX}${key}`;
  }

  private setCache<T>(key: string, data: T, duration: number = CACHE_DURATION): void {
    const now = Date.now();
    this.cache.set(this.getCacheKey(key), {
      data,
      timestamp: now,
      expiresAt: now + duration,
    });
  }

  private getCache<T>(key: string): T | null {
    const cached = this.cache.get(this.getCacheKey(key));
    if (!cached) return null;

    if (Date.now() > cached.expiresAt) {
      this.cache.delete(this.getCacheKey(key));
      return null;
    }

    return cached.data;
  }

  async searchProjects(filters: ProjectFilters = {}): Promise<ApiResponse<ProjectSearchResult>> {
    try {
      const cacheKey = `search_${JSON.stringify(filters)}`;
      const cached = this.getCache<ProjectSearchResult>(cacheKey);
      
      if (cached) {
        return {
          data: cached,
          success: true,
          message: 'Results from cache',
        };
      }

      // Combine results from different sources
      const results = await Promise.allSettled([
        this.searchGitHubProjects(filters),
        this.searchStaticProjects(filters),
      ]);

      const allProjects: Project[] = [];
      let totalCount = 0;

      // Process GitHub results
      if (results[0].status === 'fulfilled') {
        allProjects.push(...results[0].value.projects);
        totalCount += results[0].value.totalCount;
      }

      // Process static results
      if (results[1].status === 'fulfilled') {
        allProjects.push(...results[1].value.projects);
        totalCount += results[1].value.totalCount;
      }

      // Remove duplicates and sort by relevance
      const uniqueProjects = this.removeDuplicates(allProjects);
      const sortedProjects = this.sortProjectsByRelevance(uniqueProjects, filters);

      const result: ProjectSearchResult = {
        projects: sortedProjects,
        totalCount,
        hasMore: sortedProjects.length < totalCount,
      };

      // Cache the results
      this.setCache(cacheKey, result);

      return {
        data: result,
        success: true,
        message: `Found ${sortedProjects.length} projects`,
      };
    } catch (error) {
      console.error('Error searching projects:', error);
      return {
        data: { projects: [], totalCount: 0, hasMore: false },
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  async getFeaturedProjects(): Promise<ApiResponse<Project[]>> {
    try {
      const cacheKey = 'featured_projects';
      const cached = this.getCache<Project[]>(cacheKey);
      
      if (cached) {
        return {
          data: cached,
          success: true,
          message: 'Featured projects from cache',
        };
      }

      // Get trending projects from GitHub
      const trendingProjects = await githubService.getTrendingRepositories();
      
      // Mix with some curated static projects
      const staticFeatured = PROJECTS_DATA.slice(0, 3).map(project => ({
        ...project,
        source: 'static' as const,
        sourceId: project.id,
        fetchedAt: new Date().toISOString(),
        isActive: true,
      }));

      const featured = [...trendingProjects.slice(0, 6), ...staticFeatured]
        .sort((a, b) => b.stars - a.stars)
        .slice(0, 6);

      this.setCache(cacheKey, featured);

      return {
        data: featured,
        success: true,
        message: `Found ${featured.length} featured projects`,
      };
    } catch (error) {
      console.error('Error fetching featured projects:', error);
      
      // Fallback to static data
      const fallback = PROJECTS_DATA.slice(0, 6).map(project => ({
        ...project,
        source: 'static' as const,
        sourceId: project.id,
        fetchedAt: new Date().toISOString(),
        isActive: true,
      }));

      return {
        data: fallback,
        success: true,
        message: 'Showing static featured projects (fallback)',
      };
    }
  }

  async getProjectById(id: string): Promise<ApiResponse<Project | null>> {
    try {
      const cacheKey = `project_${id}`;
      const cached = this.getCache<Project>(cacheKey);
      
      if (cached) {
        return {
          data: cached,
          success: true,
          message: 'Project from cache',
        };
      }

      // Check static projects first
      const staticProject = PROJECTS_DATA.find(p => p.id === id);
      if (staticProject) {
        const project = {
          ...staticProject,
          source: 'static' as const,
          sourceId: staticProject.id,
          fetchedAt: new Date().toISOString(),
          isActive: true,
        };
        
        this.setCache(cacheKey, project);
        return {
          data: project,
          success: true,
          message: 'Static project found',
        };
      }

      // If it's a GitHub project ID, we might need to fetch it
      if (id.startsWith('github-')) {
        // For now, return null - in a real implementation, you might fetch individual repos
        return {
          data: null,
          success: false,
          message: 'GitHub project details not implemented yet',
        };
      }

      return {
        data: null,
        success: false,
        message: 'Project not found',
      };
    } catch (error) {
      console.error('Error fetching project by ID:', error);
      return {
        data: null,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  private async searchGitHubProjects(filters: ProjectFilters): Promise<ProjectSearchResult> {
    try {
      return await githubService.searchRepositories(filters);
    } catch (error) {
      console.error('Error searching GitHub projects:', error);
      return { projects: [], totalCount: 0, hasMore: false };
    }
  }

  private async searchStaticProjects(filters: ProjectFilters): Promise<ProjectSearchResult> {
    let projects = PROJECTS_DATA.map(project => ({
      ...project,
      source: 'static' as const,
      sourceId: project.id,
      fetchedAt: new Date().toISOString(),
      isActive: true,
    }));

    // Apply filters
    if (filters.query) {
      const query = filters.query.toLowerCase();
      projects = projects.filter(project =>
        project.title.toLowerCase().includes(query) ||
        project.description.toLowerCase().includes(query) ||
        project.keywords.some(keyword => keyword.toLowerCase().includes(query))
      );
    }

    if (filters.languages && filters.languages.length > 0) {
      projects = projects.filter(project =>
        filters.languages!.includes(project.language)
      );
    }

    if (filters.difficulties && filters.difficulties.length > 0) {
      projects = projects.filter(project =>
        filters.difficulties!.includes(project.difficulty)
      );
    }

    if (filters.minStars !== undefined) {
      projects = projects.filter(project => project.stars >= filters.minStars!);
    }

    if (filters.hasGoodFirstIssues) {
      projects = projects.filter(project => project.issues > 0);
    }

    return {
      projects,
      totalCount: projects.length,
      hasMore: false,
    };
  }

  private removeDuplicates(projects: Project[]): Project[] {
    const seen = new Set<string>();
    return projects.filter(project => {
      // Use repository URL or title as unique identifier
      const identifier = project.repositoryUrl || project.title.toLowerCase();
      if (seen.has(identifier)) {
        return false;
      }
      seen.add(identifier);
      return true;
    });
  }

  private sortProjectsByRelevance(projects: Project[], filters: ProjectFilters): Project[] {
    return projects.sort((a, b) => {
      // Prioritize projects with good first issues if requested
      if (filters.hasGoodFirstIssues) {
        const aHasGoodIssues = (a.goodFirstIssues?.length || 0) > 0;
        const bHasGoodIssues = (b.goodFirstIssues?.length || 0) > 0;
        if (aHasGoodIssues !== bHasGoodIssues) {
          return bHasGoodIssues ? 1 : -1;
        }
      }

      // Then sort by activity level
      const activityScore = { High: 3, Medium: 2, Low: 1 };
      const activityDiff = activityScore[b.activityLevel] - activityScore[a.activityLevel];
      if (activityDiff !== 0) return activityDiff;

      // Finally sort by stars
      return b.stars - a.stars;
    });
  }

  // Utility method to clear cache
  clearCache(): void {
    this.cache.clear();
  }

  // Utility method to get cache stats
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

export const projectService = new ProjectService();
