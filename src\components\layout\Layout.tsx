
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { Code2, Book, User, Search, Github } from "lucide-react";
import { useAuth } from "../../contexts/AuthContext";
import { authService } from "../../services/authService";
export function Layout({ children }: { children: React.ReactNode }) {
  const location = useLocation();
  const { user, isAuthenticated } = useAuth();

  const handleLogin = () => {
    const authUrl = authService.initiateGitHubLogin();
    window.location.href = authUrl;
  };
  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <header className="border-b border-gray-800">
        <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 items-center">
            <div className="flex items-center">
              <Link to="/" className="flex items-center space-x-2">
                <Code2 className="h-6 w-6 text-blue-500" />
                <span className="font-bold text-xl">IDXCodeHub</span>
              </Link>
              <div className="hidden md:flex ml-10 space-x-8">
                <NavLink
                  to="/projects"
                  active={location.pathname === "/projects"}
                >
                  Projects
                </NavLink>
                <NavLink
                  to="/open-source-guide"
                  active={location.pathname === "/open-source-guide"}
                >
                  Contribute
                </NavLink>
                <NavLink
                  to="/documentation"
                  active={location.pathname === "/documentation"}
                >
                  Docs
                </NavLink>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="relative hidden lg:block">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search projects, languages..."
                  className="bg-gray-800 rounded-full pl-10 pr-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
                  onFocus={() => window.location.href = '/projects'}
                />
              </div>
              <Link to="/projects" className="lg:hidden">
                <Search className="h-6 w-6 text-gray-400 hover:text-gray-300" />
              </Link>
              {isAuthenticated && user ? (
                <Link
                  to="/profile"
                  className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
                >
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-6 h-6 rounded-full"
                  />
                  <span className="hidden sm:inline">{user.username}</span>
                </Link>
              ) : (
                <button
                  onClick={handleLogin}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-md text-sm font-medium transition-colors"
                >
                  <Github className="h-4 w-4" />
                  <span>Sign in</span>
                </button>
              )}
              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-gray-300"
              >
                <Github className="h-6 w-6" />
              </a>
            </div>
          </div>
        </nav>
      </header>
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </main>
      <footer className="border-t border-gray-800 mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <Code2 className="h-5 w-5 text-blue-500" />
              <span className="text-gray-400">© 2025 IDXCodeHub</span>
            </div>
            <div className="flex space-x-6">
              <a href="#" className="text-gray-400 hover:text-gray-300">
                Terms
              </a>
              <a href="#" className="text-gray-400 hover:text-gray-300">
                Privacy
              </a>
              <a href="#" className="text-gray-400 hover:text-gray-300">
                Contact
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
function NavLink({
  to,
  children,
  active,
}: {
  to: string;
  children: React.ReactNode;
  active: boolean;
}) {
  return (
    <Link
      to={to}
      className={`${active ? "text-blue-500" : "text-gray-300 hover:text-gray-100"} px-3 py-2 rounded-md text-sm font-medium`}
    >
      {children}
    </Link>
  );
}
