import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  User,
  Star,
  GitBranch,
  Trash2,
  Edit3,
  Save,
  X,
  Shield,
  Bell,
  CheckCircle,
  AlertCircle,
  Loader
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { BookmarkedProject } from '../../../types/userTypes';
import { PROJECTS_DATA } from '../../../constants/projects.data';
import { useAuth } from '../../../contexts/AuthContext';

// Bookmarks Tab Component
export function BookmarksTab({ bookmarks }: { bookmarks: BookmarkedProject[] }) {
  const [searchQuery, setSearchQuery] = useState('');

  const bookmarkedProjectsWithDetails = bookmarks.map(bookmark => {
    const project = PROJECTS_DATA.find(p => p.id === bookmark.projectId);
    return {
      ...bookmark,
      project
    };
  }).filter(item => item.project); // Only include bookmarks where project exists

  const filteredBookmarks = bookmarkedProjectsWithDetails.filter(item =>
    item.project!.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.project!.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Search */}
      <div className="relative">
        <input
          type="text"
          placeholder="Search bookmarked projects..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Bookmarks List */}
      {filteredBookmarks.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredBookmarks.map((item) => (
            <div key={item.id} className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
              <img
                src={item.project!.image}
                alt={item.project!.title}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-200">
                    <Link
                      to={`/project/${item.project!.id}`}
                      className="hover:text-blue-400 transition-colors"
                    >
                      {item.project!.title}
                    </Link>
                  </h3>
                  <button className="text-gray-400 hover:text-red-400 transition-colors">
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>

                <p className="text-gray-400 text-sm mb-4 line-clamp-2">
                  {item.project!.description}
                </p>

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-4 text-gray-500">
                    <span className="flex items-center">
                      <Star className="h-3 w-3 mr-1" />
                      {item.project!.stars}
                    </span>
                    <span className="flex items-center">
                      <GitBranch className="h-3 w-3 mr-1" />
                      {item.project!.forks}
                    </span>
                    <span>{item.project!.language}</span>
                  </div>
                  <span className="text-gray-500">
                    Saved {formatDate(item.bookmarkedAt)}
                  </span>
                </div>

                {item.notes && (
                  <div className="mt-4 p-3 bg-gray-700 rounded-lg">
                    <p className="text-sm text-gray-300">{item.notes}</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            {searchQuery ? 'No matching bookmarks' : 'No bookmarked projects'}
          </h3>
          <p className="text-gray-500">
            {searchQuery
              ? 'Try adjusting your search terms.'
              : 'Start bookmarking projects to save them for later!'
            }
          </p>
        </div>
      )}
    </div>
  );
}

// Settings Tab Component
export function SettingsTab({
  user,
  isEditingFromHeader = false,
  onEditingComplete
}: {
  user: any;
  isEditingFromHeader?: boolean;
  onEditingComplete?: () => void;
}) {
  const { updateUser } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [formData, setFormData] = useState({
    name: user.name,
    bio: user.bio || '',
    location: user.location || '',
    website: user.website || '',
    publicProfile: user.preferences.publicProfile,
    emailNotifications: user.preferences.emailNotifications,
    showActivity: user.preferences.showActivity,
  });

  // Handle editing state from header button
  useEffect(() => {
    if (isEditingFromHeader) {
      setIsEditing(true);
    }
  }, [isEditingFromHeader]);

  const handleSave = async () => {
    setIsSaving(true);
    setSaveStatus('idle');

    try {
      // Prepare the update data
      const updateData = {
        name: formData.name,
        bio: formData.bio,
        location: formData.location,
        website: formData.website,
        preferences: {
          ...user.preferences,
          publicProfile: formData.publicProfile,
          emailNotifications: formData.emailNotifications,
          showActivity: formData.showActivity,
        }
      };

      await updateUser(updateData);
      setSaveStatus('success');
      setIsEditing(false);

      // Call the completion callback if provided
      if (onEditingComplete) {
        onEditingComplete();
      }

      // Clear success message after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      console.error('Failed to save user settings:', error);
      setSaveStatus('error');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: user.name,
      bio: user.bio || '',
      location: user.location || '',
      website: user.website || '',
      publicProfile: user.preferences.publicProfile,
      emailNotifications: user.preferences.emailNotifications,
      showActivity: user.preferences.showActivity,
    });
    setIsEditing(false);
    setSaveStatus('idle');

    // Call the completion callback if provided
    if (onEditingComplete) {
      onEditingComplete();
    }
  };

  return (
    <div className="space-y-8">
      {/* Save Status Message */}
      {saveStatus !== 'idle' && (
        <div className={`rounded-lg p-4 flex items-center ${
          saveStatus === 'success'
            ? 'bg-green-900 border border-green-700 text-green-200'
            : 'bg-red-900 border border-red-700 text-red-200'
        }`}>
          {saveStatus === 'success' ? (
            <CheckCircle className="h-5 w-5 mr-2" />
          ) : (
            <AlertCircle className="h-5 w-5 mr-2" />
          )}
          <span>
            {saveStatus === 'success'
              ? 'Profile updated successfully!'
              : 'Failed to update profile. Please try again.'
            }
          </span>
        </div>
      )}

      {/* Profile Settings */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold flex items-center">
            <User className="h-5 w-5 mr-2 text-blue-500" />
            Profile Information
          </h3>
          {!isEditing ? (
            <button
              onClick={() => setIsEditing(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
            >
              <Edit3 className="h-4 w-4 mr-2" />
              Edit
            </button>
          ) : (
            <div className="flex space-x-2">
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="bg-green-600 hover:bg-green-700 disabled:bg-green-800 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg flex items-center transition-colors"
              >
                {isSaving ? (
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                {isSaving ? 'Saving...' : 'Save'}
              </button>
              <button
                onClick={handleCancel}
                disabled={isSaving}
                className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-800 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg flex items-center transition-colors"
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              disabled={!isEditing}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-200 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Location</label>
            <input
              type="text"
              value={formData.location}
              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              disabled={!isEditing}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-200 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-300 mb-2">Bio</label>
            <textarea
              value={formData.bio}
              onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
              disabled={!isEditing}
              rows={3}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-200 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-300 mb-2">Website</label>
            <input
              type="url"
              value={formData.website}
              onChange={(e) => setFormData({ ...formData, website: e.target.value })}
              disabled={!isEditing}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-200 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Privacy Settings */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold mb-6 flex items-center">
          <Shield className="h-5 w-5 mr-2 text-green-500" />
          Privacy Settings
        </h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-gray-200 font-medium">Public Profile</h4>
              <p className="text-sm text-gray-400">Allow others to view your profile and activity</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={formData.publicProfile}
                onChange={(e) => setFormData({ ...formData, publicProfile: e.target.checked })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-gray-200 font-medium">Show Activity</h4>
              <p className="text-sm text-gray-400">Display your recent contributions and activity</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={formData.showActivity}
                onChange={(e) => setFormData({ ...formData, showActivity: e.target.checked })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Notification Settings */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold mb-6 flex items-center">
          <Bell className="h-5 w-5 mr-2 text-yellow-500" />
          Notification Settings
        </h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-gray-200 font-medium">Email Notifications</h4>
              <p className="text-sm text-gray-400">Receive updates about your contributions and badges</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={formData.emailNotifications}
                onChange={(e) => setFormData({ ...formData, emailNotifications: e.target.checked })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}
