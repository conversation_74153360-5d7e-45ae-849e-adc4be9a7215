import { useState, useEffect } from 'react';

/**
 * Custom hook for managing localStorage with TypeScript support
 * @param key - The localStorage key
 * @param initialValue - The initial value if key doesn't exist
 * @returns [value, setValue, removeValue]
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  // Get value from localStorage or use initial value
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Set value in localStorage and state
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  // Remove value from localStorage and reset to initial value
  const removeValue = () => {
    try {
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue, removeValue];
}

/**
 * Hook for managing multiple localStorage keys with a prefix
 */
export function useLocalStorageWithPrefix(prefix: string) {
  const getItem = <T>(key: string, defaultValue: T): T => {
    try {
      const item = window.localStorage.getItem(`${prefix}_${key}`);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${prefix}_${key}":`, error);
      return defaultValue;
    }
  };

  const setItem = <T>(key: string, value: T): void => {
    try {
      window.localStorage.setItem(`${prefix}_${key}`, JSON.stringify(value));
    } catch (error) {
      console.error(`Error setting localStorage key "${prefix}_${key}":`, error);
    }
  };

  const removeItem = (key: string): void => {
    try {
      window.localStorage.removeItem(`${prefix}_${key}`);
    } catch (error) {
      console.error(`Error removing localStorage key "${prefix}_${key}":`, error);
    }
  };

  const clear = (): void => {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(`${prefix}_`)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error(`Error clearing localStorage with prefix "${prefix}":`, error);
    }
  };

  return { getItem, setItem, removeItem, clear };
}
