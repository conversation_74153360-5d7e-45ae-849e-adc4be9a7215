import React, { useState } from 'react';
import {
  Activity,
  Award,
  BookOpen,
  Settings,
  User,
  Eye,
  GitBranch,
  Star,
  Target,
  Calendar,
  ExternalLink,
  Trash2,
  Edit3,
  Save,
  X,
  Shield,
  Bell,
  Palette,
  Globe
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { Contribution, Badge, UserBadge, BookmarkedProject } from '../../../types/userTypes';
import { PROJECTS_DATA } from '../../../constants/projects.data';

// Tab Navigation Component
export function TabNavigation({ activeTab, setActiveTab }: {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}) {
  const tabs = [
    { id: 'overview', label: 'Overview', icon: User },
    { id: 'contributions', label: 'Contributions', icon: Activity },
    { id: 'badges', label: 'Badges', icon: Award },
    { id: 'bookmarks', label: 'Bookmarks', icon: BookOpen },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  return (
    <div className="border-b border-gray-700">
      <nav className="flex space-x-8 overflow-x-auto">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          );
        })}
      </nav>
    </div>
  );
}

// Overview Tab Component
export function OverviewTab({ user, contributions, badges }: {
  user: any;
  contributions: Contribution[];
  badges: Badge[];
}) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 30) return `${diffDays} days ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  };

  const getContributionIcon = (type: string) => {
    switch (type) {
      case 'view': return <Eye className="h-4 w-4" />;
      case 'bookmark': return <BookOpen className="h-4 w-4" />;
      case 'pr': return <GitBranch className="h-4 w-4" />;
      case 'star': return <Star className="h-4 w-4" />;
      case 'issue': return <Target className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getContributionColor = (type: string) => {
    switch (type) {
      case 'view': return 'text-blue-400';
      case 'bookmark': return 'text-green-400';
      case 'pr': return 'text-purple-400';
      case 'star': return 'text-yellow-400';
      case 'issue': return 'text-orange-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Recent Activity */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Activity className="h-5 w-5 mr-2 text-blue-500" />
          Recent Activity
        </h3>
        {contributions.length > 0 ? (
          <div className="space-y-3">
            {contributions.map((contribution) => (
              <div key={contribution.id} className="flex items-center space-x-3 p-3 bg-gray-700 rounded-lg">
                <div className={`${getContributionColor(contribution.type)}`}>
                  {getContributionIcon(contribution.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-200 truncate">
                    <span className="capitalize">{contribution.type}</span> {contribution.projectTitle}
                  </p>
                  <p className="text-xs text-gray-400">{formatDate(contribution.timestamp)}</p>
                </div>
                <div className="text-xs text-blue-400">+{contribution.points}</div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-400 text-center py-8">No recent activity</p>
        )}
      </div>

      {/* Recent Badges */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Award className="h-5 w-5 mr-2 text-yellow-500" />
          Recent Badges
        </h3>
        {badges.length > 0 ? (
          <div className="grid grid-cols-2 gap-3">
            {badges.map((badge) => (
              <div key={badge.id} className="p-3 bg-gray-700 rounded-lg text-center">
                <div className="text-2xl mb-2">{badge.icon}</div>
                <h4 className="text-sm font-medium text-gray-200">{badge.name}</h4>
                <p className="text-xs text-gray-400 mt-1">{badge.description}</p>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-400 text-center py-8">No badges earned yet</p>
        )}
      </div>
    </div>
  );
}

// Contributions Tab Component
export function ContributionsTab({ contributions }: { contributions: Contribution[] }) {
  const [filter, setFilter] = useState('all');

  const filteredContributions = filter === 'all'
    ? contributions
    : contributions.filter(c => c.type === filter);

  const contributionTypes = [
    { value: 'all', label: 'All', count: contributions.length },
    { value: 'view', label: 'Views', count: contributions.filter(c => c.type === 'view').length },
    { value: 'bookmark', label: 'Bookmarks', count: contributions.filter(c => c.type === 'bookmark').length },
    { value: 'pr', label: 'Pull Requests', count: contributions.filter(c => c.type === 'pr').length },
    { value: 'star', label: 'Stars', count: contributions.filter(c => c.type === 'star').length },
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getContributionIcon = (type: string) => {
    switch (type) {
      case 'view': return <Eye className="h-4 w-4" />;
      case 'bookmark': return <BookOpen className="h-4 w-4" />;
      case 'pr': return <GitBranch className="h-4 w-4" />;
      case 'star': return <Star className="h-4 w-4" />;
      case 'issue': return <Target className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getContributionColor = (type: string) => {
    switch (type) {
      case 'view': return 'text-blue-400 bg-blue-900/20';
      case 'bookmark': return 'text-green-400 bg-green-900/20';
      case 'pr': return 'text-purple-400 bg-purple-900/20';
      case 'star': return 'text-yellow-400 bg-yellow-900/20';
      case 'issue': return 'text-orange-400 bg-orange-900/20';
      default: return 'text-gray-400 bg-gray-900/20';
    }
  };

  return (
    <div className="space-y-6">
      {/* Filter Tabs */}
      <div className="flex flex-wrap gap-2">
        {contributionTypes.map((type) => (
          <button
            key={type.value}
            onClick={() => setFilter(type.value)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === type.value
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            {type.label} ({type.count})
          </button>
        ))}
      </div>

      {/* Contributions List */}
      <div className="bg-gray-800 rounded-lg border border-gray-700">
        {filteredContributions.length > 0 ? (
          <div className="divide-y divide-gray-700">
            {filteredContributions.map((contribution) => (
              <div key={contribution.id} className="p-4 hover:bg-gray-700/50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`p-2 rounded-lg ${getContributionColor(contribution.type)}`}>
                      {getContributionIcon(contribution.type)}
                    </div>
                    <div>
                      <h4 className="text-gray-200 font-medium">
                        <span className="capitalize">{contribution.type}</span> {contribution.projectTitle}
                      </h4>
                      <p className="text-sm text-gray-400">{formatDate(contribution.timestamp)}</p>
                      {contribution.description && (
                        <p className="text-sm text-gray-500 mt-1">{contribution.description}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-blue-400">+{contribution.points} pts</span>
                    {contribution.projectUrl && (
                      <a
                        href={contribution.projectUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-gray-300"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-8 text-center">
            <Activity className="h-12 w-12 text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-300 mb-2">No contributions found</h3>
            <p className="text-gray-500">
              {filter === 'all'
                ? 'Start exploring projects to see your activity here!'
                : `No ${filter} contributions yet.`
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

// Badges Tab Component
export function BadgesTab({ badges, userBadges }: { badges: Badge[]; userBadges: UserBadge[] }) {
  const [filter, setFilter] = useState('all');

  const getBadgeRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-600 text-gray-200 border-gray-500';
      case 'uncommon': return 'bg-green-600 text-green-100 border-green-500';
      case 'rare': return 'bg-blue-600 text-blue-100 border-blue-500';
      case 'epic': return 'bg-purple-600 text-purple-100 border-purple-500';
      case 'legendary': return 'bg-yellow-600 text-yellow-100 border-yellow-500';
      default: return 'bg-gray-600 text-gray-200 border-gray-500';
    }
  };

  const rarityFilters = [
    { value: 'all', label: 'All Badges' },
    { value: 'common', label: 'Common' },
    { value: 'uncommon', label: 'Uncommon' },
    { value: 'rare', label: 'Rare' },
    { value: 'epic', label: 'Epic' },
    { value: 'legendary', label: 'Legendary' },
  ];

  const filteredBadges = filter === 'all'
    ? badges
    : badges.filter(b => b.rarity === filter);

  return (
    <div className="space-y-6">
      {/* Filter Tabs */}
      <div className="flex flex-wrap gap-2">
        {rarityFilters.map((rarityFilter) => (
          <button
            key={rarityFilter.value}
            onClick={() => setFilter(rarityFilter.value)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === rarityFilter.value
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            {rarityFilter.label}
          </button>
        ))}
      </div>

      {/* Badges Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredBadges.map((badge) => {
          const userBadge = userBadges.find(ub => ub.badgeId === badge.id);
          const isEarned = !!userBadge;

          return (
            <div
              key={badge.id}
              className={`p-6 rounded-lg border-2 transition-all ${
                isEarned
                  ? `${getBadgeRarityColor(badge.rarity)} shadow-lg`
                  : 'bg-gray-800 border-gray-600 text-gray-400'
              }`}
            >
              <div className="text-center">
                <div className={`text-4xl mb-3 ${isEarned ? '' : 'grayscale opacity-50'}`}>
                  {badge.icon}
                </div>
                <h3 className={`text-lg font-semibold mb-2 ${isEarned ? '' : 'text-gray-500'}`}>
                  {badge.name}
                </h3>
                <p className={`text-sm mb-3 ${isEarned ? '' : 'text-gray-600'}`}>
                  {badge.description}
                </p>

                {/* Badge Details */}
                <div className="space-y-2">
                  <div className={`inline-block px-2 py-1 rounded text-xs font-medium ${
                    isEarned ? getBadgeRarityColor(badge.rarity) : 'bg-gray-700 text-gray-400'
                  }`}>
                    {badge.rarity.charAt(0).toUpperCase() + badge.rarity.slice(1)}
                  </div>

                  {isEarned && userBadge && (
                    <div className="text-xs text-gray-300">
                      Earned {new Date(userBadge.unlockedAt).toLocaleDateString()}
                    </div>
                  )}

                  {!isEarned && (
                    <div className="text-xs text-gray-500">
                      Requirement: {badge.requirement.target} {badge.requirement.metric}
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredBadges.length === 0 && (
        <div className="text-center py-12">
          <Award className="h-12 w-12 text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">No badges found</h3>
          <p className="text-gray-500">
            {filter === 'all'
              ? 'Start contributing to earn your first badge!'
              : `No ${filter} badges available.`
            }
          </p>
        </div>
      )}
    </div>
  );
}