import { Project } from "../types/project.types";

export const featuredProjects: Project[] = [
    {
      id: "1",
      title: "React Dashboard",
      description:
        "A modern dashboard template with dark mode and customizable components",
      image:
        "https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 245,
      forks: 57,
      tags: ["React", "TypeScript", "Tailwind"],
      language: "TypeScript",
      difficulty: "Intermediate",
      domain: "Web Development",
      activityLevel: "High",
      lastUpdated: "2024-01-15",
      contributors: 12,
      issues: 8,
      license: "MIT",
      keywords: ["dashboard", "admin", "template", "dark-mode", "responsive"]
    },
    {
      id: "6",
      title: "Machine Learning Toolkit",
      description: "Python toolkit for machine learning experiments and model training",
      image:
        "https://images.unsplash.com/photo-**********-aa79dcee981c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 567,
      forks: 134,
      tags: ["Python", "ML", "AI", "TensorFlow"],
      language: "Python",
      difficulty: "Advanced",
      domain: "AI/ML",
      activityLevel: "High",
      lastUpdated: "2024-01-20",
      contributors: 32,
      issues: 22,
      license: "Apache-2.0",
      keywords: ["machine-learning", "ai", "tensorflow", "pytorch", "neural-networks"]
    },
    {
      id: "8",
      title: "Blockchain Wallet",
      description: "Secure cryptocurrency wallet with multi-chain support",
      image:
        "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 445,
      forks: 112,
      tags: ["Blockchain", "Crypto", "Web3"],
      language: "Solidity",
      difficulty: "Advanced",
      domain: "Blockchain",
      activityLevel: "High",
      lastUpdated: "2024-01-16",
      contributors: 21,
      issues: 18,
      license: "GPL-3.0",
      keywords: ["blockchain", "cryptocurrency", "wallet", "web3", "ethereum"]
    }
  ];