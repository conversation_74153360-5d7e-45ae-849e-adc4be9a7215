export interface Project {
  id: string;
  title: string;
  description: string;
  image: string;
  stars: number;
  forks: number;
  tags: string[];
  language: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  domain: string;
  activityLevel: 'Low' | 'Medium' | 'High';
  lastUpdated: string;
  contributors: number;
  issues: number;
  license: string;
  keywords: string[];
  // Extended fields for detailed view
  longDescription?: string;
  readme?: string;
  repositoryUrl?: string;
  demoUrl?: string;
  documentationUrl?: string;
  contributionGuide?: string;
  setupInstructions?: string;
  technologies?: string[];
  prerequisites?: string[];
  goodFirstIssues?: Issue[];
  maintainers?: Maintainer[];
  recentCommits?: Commit[];
  projectStructure?: string;
  codeOfConduct?: string;
  changelog?: string;
  roadmap?: string;
}

export interface Issue {
  id: string;
  title: string;
  description: string;
  labels: string[];
  difficulty: 'Easy' | 'Medium' | 'Hard';
  estimatedTime: string;
  url: string;
  createdAt: string;
  author: string;
}

export interface Maintainer {
  id: string;
  name: string;
  avatar: string;
  role: string;
  githubUrl: string;
  contributions: number;
}

export interface Commit {
  id: string;
  message: string;
  author: string;
  date: string;
  url: string;
}

export const projectslist: Project[] = [
    {
      id: "1",
      title: "React Dashboard",
      description:
        "A modern dashboard template with dark mode and customizable components",
      image:
        "https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 245,
      forks: 57,
      tags: ["React", "TypeScript", "Tailwind"],
      language: "TypeScript",
      difficulty: "Intermediate",
      domain: "Web Development",
      activityLevel: "High",
      lastUpdated: "2024-01-15",
      contributors: 12,
      issues: 8,
      license: "MIT",
      keywords: ["dashboard", "admin", "template", "dark-mode", "responsive"],
      longDescription: "A comprehensive React dashboard template built with modern technologies. Features include customizable components, dark/light mode toggle, responsive design, and a clean, professional interface perfect for admin panels and data visualization applications.",
      repositoryUrl: "https://github.com/example/react-dashboard",
      demoUrl: "https://react-dashboard-demo.vercel.app",
      documentationUrl: "https://react-dashboard-docs.vercel.app",
      technologies: ["React 18", "TypeScript", "Tailwind CSS", "Vite", "React Router", "Recharts", "Framer Motion"],
      prerequisites: ["Node.js 16+", "npm or yarn", "Basic React knowledge"],
      setupInstructions: `## Getting Started

1. Clone the repository:
\`\`\`bash
git clone https://github.com/example/react-dashboard.git
cd react-dashboard
\`\`\`

2. Install dependencies:
\`\`\`bash
npm install
\`\`\`

3. Start the development server:
\`\`\`bash
npm run dev
\`\`\`

4. Open http://localhost:3000 in your browser`,
      contributionGuide: `## Contributing to React Dashboard

We welcome contributions! Here's how you can help:

### Getting Started
1. Fork the repository
2. Create a feature branch: \`git checkout -b feature/amazing-feature\`
3. Make your changes
4. Run tests: \`npm test\`
5. Commit your changes: \`git commit -m 'Add amazing feature'\`
6. Push to the branch: \`git push origin feature/amazing-feature\`
7. Open a Pull Request

### What We're Looking For
- Bug fixes
- New dashboard components
- Performance improvements
- Documentation updates
- Accessibility improvements

### Code Style
- Use TypeScript for all new code
- Follow the existing code style
- Add tests for new features
- Update documentation as needed`,
      goodFirstIssues: [
        {
          id: "issue-1",
          title: "Add dark mode toggle animation",
          description: "Improve the dark mode toggle with a smooth animation transition. Currently it's instant, but a fade or slide animation would enhance UX.",
          labels: ["good first issue", "enhancement", "ui"],
          difficulty: "Easy",
          estimatedTime: "2-3 hours",
          url: "https://github.com/example/react-dashboard/issues/23",
          createdAt: "2024-01-10",
          author: "maintainer1"
        },
        {
          id: "issue-2",
          title: "Fix responsive layout on mobile devices",
          description: "The sidebar doesn't collapse properly on mobile screens smaller than 768px. Need to implement a mobile-friendly navigation.",
          labels: ["good first issue", "bug", "responsive"],
          difficulty: "Medium",
          estimatedTime: "4-6 hours",
          url: "https://github.com/example/react-dashboard/issues/31",
          createdAt: "2024-01-12",
          author: "user123"
        },
        {
          id: "issue-3",
          title: "Add loading states to dashboard cards",
          description: "Dashboard cards should show skeleton loaders while data is being fetched. This will improve perceived performance.",
          labels: ["good first issue", "enhancement", "ux"],
          difficulty: "Easy",
          estimatedTime: "3-4 hours",
          url: "https://github.com/example/react-dashboard/issues/45",
          createdAt: "2024-01-14",
          author: "designer_dev"
        }
      ],
      maintainers: [
        {
          id: "maintainer1",
          name: "Alex Johnson",
          avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150",
          role: "Lead Maintainer",
          githubUrl: "https://github.com/alexjohnson",
          contributions: 156
        },
        {
          id: "maintainer2",
          name: "Sarah Chen",
          avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150",
          role: "UI/UX Lead",
          githubUrl: "https://github.com/sarahchen",
          contributions: 89
        }
      ],
      recentCommits: [
        {
          id: "commit1",
          message: "feat: add new chart component with customizable colors",
          author: "Alex Johnson",
          date: "2024-01-15",
          url: "https://github.com/example/react-dashboard/commit/abc123"
        },
        {
          id: "commit2",
          message: "fix: resolve sidebar navigation bug on mobile",
          author: "Sarah Chen",
          date: "2024-01-14",
          url: "https://github.com/example/react-dashboard/commit/def456"
        },
        {
          id: "commit3",
          message: "docs: update installation instructions",
          author: "Alex Johnson",
          date: "2024-01-13",
          url: "https://github.com/example/react-dashboard/commit/ghi789"
        }
      ],
      projectStructure: `## Project Structure

\`\`\`
src/
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI elements
│   ├── charts/         # Chart components
│   └── layout/         # Layout components
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
└── styles/             # Global styles
\`\`\``,
      readme: `# React Dashboard

A modern, responsive dashboard template built with React, TypeScript, and Tailwind CSS.

## Features

- 🌙 Dark/Light mode toggle
- 📱 Fully responsive design
- 📊 Interactive charts and graphs
- 🎨 Customizable components
- ⚡ Fast performance with Vite
- 🔧 TypeScript for better development experience

## Quick Start

\`\`\`bash
npm install
npm run dev
\`\`\`

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## License

MIT License - see [LICENSE](LICENSE) for details.`
    },
    {
      id: "2",
      title: "API Integration Tool",
      description: "Simple and powerful API testing and integration toolkit",
      image:
        "https://images.unsplash.com/photo-**********-aa79dcee981c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 189,
      forks: 34,
      tags: ["API", "Testing", "Node.js"],
      language: "JavaScript",
      difficulty: "Beginner",
      domain: "DevOps",
      activityLevel: "Medium",
      lastUpdated: "2024-01-10",
      contributors: 8,
      issues: 5,
      license: "Apache-2.0",
      keywords: ["api", "testing", "integration", "postman", "rest"]
    },
    {
      id: "3",
      title: "CSS Animation Library",
      description: "Collection of reusable CSS animations and transitions",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 156,
      forks: 23,
      tags: ["CSS", "Animation", "Frontend"],
      language: "CSS",
      difficulty: "Beginner",
      domain: "Web Development",
      activityLevel: "Low",
      lastUpdated: "2023-12-20",
      contributors: 5,
      issues: 3,
      license: "MIT",
      keywords: ["animation", "css", "transitions", "effects", "frontend"]
    },
    {
      id: "4",
      title: "Authentication System",
      description: "Secure authentication system with multiple providers",
      image:
        "https://images.unsplash.com/photo-**********-aa79dcee981c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 423,
      forks: 98,
      tags: ["Auth", "Security", "Node.js"],
      language: "JavaScript",
      difficulty: "Advanced",
      domain: "Backend",
      activityLevel: "High",
      lastUpdated: "2024-01-18",
      contributors: 25,
      issues: 15,
      license: "MIT",
      keywords: ["authentication", "oauth", "jwt", "security", "passport"]
    },
    {
      id: "5",
      title: "Data Visualization Tool",
      description: "Interactive data visualization library with multiple chart types",
      image:
        "https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 312,
      forks: 89,
      tags: ["D3.js", "SVG", "Charts"],
      language: "JavaScript",
      difficulty: "Intermediate",
      domain: "Data Science",
      activityLevel: "Medium",
      lastUpdated: "2024-01-12",
      contributors: 18,
      issues: 12,
      license: "BSD-3-Clause",
      keywords: ["visualization", "charts", "d3", "svg", "analytics"]
    },
    {
      id: "6",
      title: "Machine Learning Toolkit",
      description: "Python toolkit for machine learning experiments and model training",
      image:
        "https://images.unsplash.com/photo-**********-aa79dcee981c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 567,
      forks: 134,
      tags: ["Python", "ML", "AI", "TensorFlow"],
      language: "Python",
      difficulty: "Advanced",
      domain: "AI/ML",
      activityLevel: "High",
      lastUpdated: "2024-01-20",
      contributors: 32,
      issues: 22,
      license: "Apache-2.0",
      keywords: ["machine-learning", "ai", "tensorflow", "pytorch", "neural-networks"],
      longDescription: "A comprehensive Python toolkit designed for machine learning researchers and practitioners. Provides high-level APIs for common ML tasks, pre-built models, and utilities for data preprocessing, model training, and evaluation.",
      repositoryUrl: "https://github.com/example/ml-toolkit",
      demoUrl: "https://ml-toolkit-demo.herokuapp.com",
      documentationUrl: "https://ml-toolkit.readthedocs.io",
      technologies: ["Python 3.9+", "TensorFlow", "PyTorch", "Scikit-learn", "NumPy", "Pandas", "Jupyter"],
      prerequisites: ["Python 3.9+", "pip", "Basic ML knowledge", "CUDA (optional for GPU)"],
      setupInstructions: `## Installation

1. Clone the repository:
\`\`\`bash
git clone https://github.com/example/ml-toolkit.git
cd ml-toolkit
\`\`\`

2. Create a virtual environment:
\`\`\`bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\\Scripts\\activate
\`\`\`

3. Install dependencies:
\`\`\`bash
pip install -r requirements.txt
\`\`\`

4. Install the package:
\`\`\`bash
pip install -e .
\`\`\`

5. Run tests:
\`\`\`bash
pytest tests/
\`\`\``,
      contributionGuide: `## Contributing to ML Toolkit

### Getting Started
1. Fork the repository
2. Create a feature branch: \`git checkout -b feature/new-algorithm\`
3. Set up development environment (see Setup Guide)
4. Make your changes
5. Add tests for new functionality
6. Run the test suite: \`pytest\`
7. Update documentation if needed
8. Submit a pull request

### Areas for Contribution
- New ML algorithms and models
- Performance optimizations
- Documentation improvements
- Bug fixes and testing
- Example notebooks and tutorials

### Code Standards
- Follow PEP 8 style guidelines
- Add type hints to all functions
- Include docstrings for all public methods
- Maintain test coverage above 90%`,
      goodFirstIssues: [
        {
          id: "ml-issue-1",
          title: "Add support for additional evaluation metrics",
          description: "Implement F1-score, precision, and recall metrics for classification tasks. Currently only accuracy is supported.",
          labels: ["good first issue", "enhancement", "metrics"],
          difficulty: "Easy",
          estimatedTime: "3-4 hours",
          url: "https://github.com/example/ml-toolkit/issues/45",
          createdAt: "2024-01-18",
          author: "ml_researcher"
        },
        {
          id: "ml-issue-2",
          title: "Improve error handling in data preprocessing",
          description: "Add better error messages and validation for data preprocessing functions. Users should get clear feedback when data format is incorrect.",
          labels: ["good first issue", "bug", "preprocessing"],
          difficulty: "Medium",
          estimatedTime: "4-6 hours",
          url: "https://github.com/example/ml-toolkit/issues/52",
          createdAt: "2024-01-19",
          author: "data_scientist"
        }
      ],
      maintainers: [
        {
          id: "ml-maintainer1",
          name: "Dr. Emily Rodriguez",
          avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150",
          role: "Lead ML Engineer",
          githubUrl: "https://github.com/emilyrodriguez",
          contributions: 234
        }
      ],
      readme: `# ML Toolkit

A comprehensive Python toolkit for machine learning experiments and model training.

## Features

- 🧠 Pre-built models for common tasks
- 📊 Data preprocessing utilities
- 🔧 Model training and evaluation tools
- 📈 Visualization and plotting functions
- 🚀 Easy-to-use high-level APIs

## Quick Example

\`\`\`python
from ml_toolkit import LinearClassifier, load_data

# Load and preprocess data
X, y = load_data('dataset.csv')

# Train model
model = LinearClassifier()
model.fit(X, y)

# Evaluate
accuracy = model.evaluate(X_test, y_test)
print(f"Accuracy: {accuracy:.2f}")
\`\`\`

## Installation

\`\`\`bash
pip install ml-toolkit
\`\`\`

## Documentation

Visit our [documentation](https://ml-toolkit.readthedocs.io) for detailed guides and API reference.`
    },
    {
      id: "7",
      title: "Mobile App Template",
      description: "Cross-platform mobile app template with navigation and state management",
      image:
        "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 289,
      forks: 67,
      tags: ["React Native", "Mobile", "Redux"],
      language: "TypeScript",
      difficulty: "Intermediate",
      domain: "Mobile Development",
      activityLevel: "Medium",
      lastUpdated: "2024-01-08",
      contributors: 14,
      issues: 9,
      license: "MIT",
      keywords: ["mobile", "react-native", "cross-platform", "ios", "android"]
    },
    {
      id: "8",
      title: "Blockchain Wallet",
      description: "Secure cryptocurrency wallet with multi-chain support",
      image:
        "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 445,
      forks: 112,
      tags: ["Blockchain", "Crypto", "Web3"],
      language: "Solidity",
      difficulty: "Advanced",
      domain: "Blockchain",
      activityLevel: "High",
      lastUpdated: "2024-01-16",
      contributors: 21,
      issues: 18,
      license: "GPL-3.0",
      keywords: ["blockchain", "cryptocurrency", "wallet", "web3", "ethereum"]
    },
    {
      id: "9",
      title: "Documentation Generator",
      description: "Automated documentation generator for code projects",
      image:
        "https://images.unsplash.com/photo-**********-29b0f74f9713?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 178,
      forks: 45,
      tags: ["Documentation", "CLI", "Markdown"],
      language: "Go",
      difficulty: "Beginner",
      domain: "DevOps",
      activityLevel: "Low",
      lastUpdated: "2023-12-28",
      contributors: 7,
      issues: 4,
      license: "MIT",
      keywords: ["documentation", "generator", "markdown", "cli", "automation"]
    },
    {
      id: "10",
      title: "Game Engine 2D",
      description: "Lightweight 2D game engine for indie game development",
      image:
        "https://images.unsplash.com/photo-1511512578047-dfb367046420?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      stars: 356,
      forks: 78,
      tags: ["C++", "Game", "Graphics"],
      language: "C++",
      difficulty: "Advanced",
      domain: "Game Development",
      activityLevel: "Medium",
      lastUpdated: "2024-01-05",
      contributors: 16,
      issues: 11,
      license: "MIT",
      keywords: ["game-engine", "2d", "graphics", "indie", "gamedev"]
    }
  ];
