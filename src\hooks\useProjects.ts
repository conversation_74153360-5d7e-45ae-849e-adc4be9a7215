import { useState, useEffect, useCallback } from 'react';
import { Project, ProjectFilters, ProjectSearchResult } from '@/types/project.types';
import { projectService } from '@/services/projectService';

interface UseProjectsState {
  projects: Project[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  hasMore: boolean;
}

interface UseProjectsReturn extends UseProjectsState {
  searchProjects: (filters: ProjectFilters) => Promise<void>;
  refreshProjects: () => Promise<void>;
  clearError: () => void;
}

export function useProjects(initialFilters: ProjectFilters = {}): UseProjectsReturn {
  const [state, setState] = useState<UseProjectsState>({
    projects: [],
    loading: false,
    error: null,
    totalCount: 0,
    hasMore: false,
  });

  const searchProjects = useCallback(async (filters: ProjectFilters) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await projectService.searchProjects(filters);
      
      if (response.success) {
        setState(prev => ({
          ...prev,
          projects: response.data.projects,
          totalCount: response.data.totalCount,
          hasMore: response.data.hasMore,
          loading: false,
        }));
      } else {
        setState(prev => ({
          ...prev,
          error: response.error || 'Failed to search projects',
          loading: false,
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        loading: false,
      }));
    }
  }, []);

  const refreshProjects = useCallback(async () => {
    await searchProjects(initialFilters);
  }, [searchProjects, initialFilters]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Initial load
  useEffect(() => {
    searchProjects(initialFilters);
  }, [searchProjects, initialFilters]);

  return {
    ...state,
    searchProjects,
    refreshProjects,
    clearError,
  };
}

interface UseFeaturedProjectsState {
  projects: Project[];
  loading: boolean;
  error: string | null;
}

interface UseFeaturedProjectsReturn extends UseFeaturedProjectsState {
  refreshFeatured: () => Promise<void>;
  clearError: () => void;
}

export function useFeaturedProjects(): UseFeaturedProjectsReturn {
  const [state, setState] = useState<UseFeaturedProjectsState>({
    projects: [],
    loading: false,
    error: null,
  });

  const fetchFeatured = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await projectService.getFeaturedProjects();
      
      if (response.success) {
        setState(prev => ({
          ...prev,
          projects: response.data,
          loading: false,
        }));
      } else {
        setState(prev => ({
          ...prev,
          error: response.error || 'Failed to fetch featured projects',
          loading: false,
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        loading: false,
      }));
    }
  }, []);

  const refreshFeatured = useCallback(async () => {
    await fetchFeatured();
  }, [fetchFeatured]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Initial load
  useEffect(() => {
    fetchFeatured();
  }, [fetchFeatured]);

  return {
    ...state,
    refreshFeatured,
    clearError,
  };
}

interface UseProjectDetailsState {
  project: Project | null;
  loading: boolean;
  error: string | null;
}

interface UseProjectDetailsReturn extends UseProjectDetailsState {
  refreshProject: () => Promise<void>;
  clearError: () => void;
}

export function useProjectDetails(projectId: string): UseProjectDetailsReturn {
  const [state, setState] = useState<UseProjectDetailsState>({
    project: null,
    loading: false,
    error: null,
  });

  const fetchProject = useCallback(async () => {
    if (!projectId) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await projectService.getProjectById(projectId);
      
      if (response.success) {
        setState(prev => ({
          ...prev,
          project: response.data,
          loading: false,
        }));
      } else {
        setState(prev => ({
          ...prev,
          error: response.error || 'Failed to fetch project details',
          loading: false,
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        loading: false,
      }));
    }
  }, [projectId]);

  const refreshProject = useCallback(async () => {
    await fetchProject();
  }, [fetchProject]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Initial load
  useEffect(() => {
    fetchProject();
  }, [fetchProject]);

  return {
    ...state,
    refreshProject,
    clearError,
  };
}

// Utility hook for project statistics
export function useProjectStats() {
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeProjects: 0,
    totalStars: 0,
    totalContributors: 0,
  });

  const calculateStats = useCallback((projects: Project[]) => {
    const totalProjects = projects.length;
    const activeProjects = projects.filter(p => p.isActive).length;
    const totalStars = projects.reduce((sum, p) => sum + p.stars, 0);
    const totalContributors = projects.reduce((sum, p) => sum + p.contributors, 0);

    setStats({
      totalProjects,
      activeProjects,
      totalStars,
      totalContributors,
    });
  }, []);

  return {
    stats,
    calculateStats,
  };
}
