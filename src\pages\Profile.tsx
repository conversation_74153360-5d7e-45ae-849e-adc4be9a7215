import React, { useState, useEffect } from "react";
import {
  <PERSON>r,
  <PERSON>ting<PERSON>,
  Star,
  GitBranch,
  Eye,
  Github,
  MapPin,
  Globe,
  Calendar,
  Award,
  TrendingUp,
  BookOpen,
  Activity,
  Target,
  Zap,
  Trophy,
  Gift,
  ExternalLink,
  Edit3,
  LogOut,
  Shield,
  Mail,
  Link as LinkIcon
} from "lucide-react";
import { useAuth } from "../contexts/AuthContext";
import { authService } from "../services/authService";
import { Link, useNavigate } from "react-router-dom";
import { Contribution, Badge, UserBadge, BookmarkedProject, ActivityFeed } from "../types/userTypes";
import { PROJECTS_DATA } from "../constants/projects.data";
import { TabNavigation, OverviewTab, ContributionsTab, BadgesTab } from "../components/features/profile/ProfileTabs";
import { BookmarksTab, SettingsTab } from "../components/features/profile/ProfileTabsExtended";

export function Profile() {
  const { user, isAuthenticated, logout, getUserContributions, getUserBadges, getBookmarkedProjects } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [contributions, setContributions] = useState<Contribution[]>([]);
  const [badges, setBadges] = useState<Badge[]>([]);
  const [userBadges, setUserBadges] = useState<UserBadge[]>([]);
  const [bookmarkedProjects, setBookmarkedProjects] = useState<BookmarkedProject[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditingProfile, setIsEditingProfile] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      // Show login prompt instead of redirecting
      setLoading(false);
      return;
    }

    const loadUserData = async () => {
      try {
        const [contribs, badgeData, bookmarks] = await Promise.all([
          getUserContributions(),
          getUserBadges(),
          getBookmarkedProjects(),
        ]);

        setContributions(contribs);
        setBadges(badgeData.badges);
        setUserBadges(badgeData.userBadges);
        setBookmarkedProjects(bookmarks);
      } catch (error) {
        console.error('Failed to load user data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, [isAuthenticated, getUserContributions, getUserBadges, getBookmarkedProjects]);

  const handleLogin = () => {
    const authUrl = authService.initiateGitHubLogin();
    window.location.href = authUrl;
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const handleEditProfile = () => {
    setIsEditingProfile(true);
    setActiveTab('settings');
  };

  if (!isAuthenticated) {
    return (
      <div className="text-center py-12">
        <Github className="h-16 w-16 text-gray-500 mx-auto mb-6" />
        <h1 className="text-3xl font-bold text-gray-300 mb-4">Sign in to view your profile</h1>
        <p className="text-gray-500 mb-8 max-w-md mx-auto">
          Connect your GitHub account to track your contributions, earn badges, and bookmark your favorite projects.
        </p>
        <button
          onClick={handleLogin}
          className="bg-gray-800 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center transition-colors"
        >
          <Github className="h-5 w-5 mr-2" />
          Sign in with GitHub
        </button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-gray-400">Loading your profile...</p>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-400">User data not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Profile Header */}
      <ProfileHeader user={user} onLogout={handleLogout} onEditProfile={handleEditProfile} />

      {/* Stats Grid */}
      <StatsGrid user={user} />

      {/* Tab Navigation */}
      <TabNavigation activeTab={activeTab} setActiveTab={setActiveTab} />

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === 'overview' && (
          <OverviewTab
            user={user}
            contributions={contributions.slice(0, 10)}
            badges={badges.slice(0, 6)}
          />
        )}
        {activeTab === 'contributions' && <ContributionsTab contributions={contributions} />}
        {activeTab === 'badges' && <BadgesTab badges={badges} userBadges={userBadges} />}
        {activeTab === 'bookmarks' && <BookmarksTab bookmarks={bookmarkedProjects} />}
        {activeTab === 'settings' && (
          <SettingsTab
            user={user}
            isEditingFromHeader={isEditingProfile}
            onEditingComplete={() => setIsEditingProfile(false)}
          />
        )}
      </div>
    </div>
  );
}

// Profile Header Component
function ProfileHeader({ user, onLogout, onEditProfile }: {
  user: any;
  onLogout: () => void;
  onEditProfile: () => void;
}) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getLevelProgress = (points: number) => {
    const levels = [0, 100, 250, 500, 1000, 2000, 3500, 5500, 8000, 12000];
    const currentLevel = user.stats.level;
    const currentLevelThreshold = levels[currentLevel - 1] || 0;
    const nextLevelThreshold = levels[currentLevel] || levels[levels.length - 1];

    if (currentLevel >= levels.length) return 100;

    const progress = ((points - currentLevelThreshold) / (nextLevelThreshold - currentLevelThreshold)) * 100;
    return Math.min(100, Math.max(0, progress));
  };

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        <div className="flex flex-col sm:flex-row items-start gap-6">
          <img
            src={user.avatar}
            alt={user.name}
            className="w-24 h-24 rounded-full border-4 border-gray-600"
          />
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-3xl font-bold text-gray-100">{user.name}</h1>
              <span className="px-3 py-1 text-sm rounded-full bg-blue-600 text-blue-100">
                Level {user.stats.level}
              </span>
            </div>
            <p className="text-gray-400 text-lg mb-3">@{user.username}</p>
            {user.bio && <p className="text-gray-300 mb-4 max-w-2xl">{user.bio}</p>}

            {/* Level Progress */}
            <div className="mb-4">
              <div className="flex items-center justify-between text-sm text-gray-400 mb-1">
                <span>Level Progress</span>
                <span>{user.stats.totalPoints} points</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getLevelProgress(user.stats.totalPoints)}%` }}
                ></div>
              </div>
            </div>

            <div className="flex flex-wrap gap-4 text-sm text-gray-400">
              {user.location && (
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-1" />
                  {user.location}
                </div>
              )}
              {user.website && (
                <div className="flex items-center">
                  <Globe className="h-4 w-4 mr-1" />
                  <a href={user.website} target="_blank" rel="noopener noreferrer" className="hover:text-blue-400">
                    {user.website.replace(/^https?:\/\//, '')}
                  </a>
                </div>
              )}
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Joined {formatDate(user.joinedAt)}
              </div>
              <div className="flex items-center">
                <Github className="h-4 w-4 mr-1" />
                <a href={user.githubUrl} target="_blank" rel="noopener noreferrer" className="hover:text-blue-400">
                  GitHub Profile
                </a>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={onEditProfile}
            className="bg-gray-700 hover:bg-gray-600 text-gray-300 px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Edit3 className="h-4 w-4 mr-2" />
            Edit Profile
          </button>
          <button
            onClick={onLogout}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Sign Out
          </button>
        </div>
      </div>
    </div>
  );
}

// Stats Grid Component
function StatsGrid({ user }: { user: any }) {
  const stats = [
    { icon: Activity, label: 'Total Contributions', value: user.stats.totalContributions, color: 'text-blue-400' },
    { icon: Eye, label: 'Projects Viewed', value: user.stats.projectsViewed, color: 'text-purple-400' },
    { icon: BookOpen, label: 'Bookmarked', value: user.stats.projectsBookmarked, color: 'text-green-400' },
    { icon: GitBranch, label: 'PRs Submitted', value: user.stats.prsSubmitted, color: 'text-orange-400' },
    { icon: Target, label: 'Issues Opened', value: user.stats.issuesOpened, color: 'text-red-400' },
    { icon: Zap, label: 'Current Streak', value: `${user.stats.streakDays} days`, color: 'text-yellow-400' },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
      {stats.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <div key={index} className="bg-gray-800 rounded-lg p-4 text-center border border-gray-700">
            <Icon className={`h-6 w-6 mx-auto mb-2 ${stat.color}`} />
            <div className="text-xl font-bold text-gray-200">{stat.value}</div>
            <div className="text-sm text-gray-400">{stat.label}</div>
          </div>
        );
      })}
    </div>
  );
}
