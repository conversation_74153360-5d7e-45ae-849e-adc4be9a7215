import React from "react";
import { Link } from "react-router-dom";
import { Star, GitBranch, Clock, Users, AlertCircle } from "lucide-react";
import { Project } from "../../../types/project.types";

interface ProjectCardProps extends Project {}
export function ProjectCard({
  id,
  title,
  description,
  image,
  stars,
  forks,
  tags,
  language,
  difficulty,
  domain,
  activityLevel,
  lastUpdated,
  contributors,
  issues,
}: ProjectCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 30) return `${diffDays} days ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'text-green-400 bg-green-900/20';
      case 'Intermediate': return 'text-yellow-400 bg-yellow-900/20';
      case 'Advanced': return 'text-red-400 bg-red-900/20';
      default: return 'text-gray-400 bg-gray-900/20';
    }
  };

  const getActivityColor = (level: string) => {
    switch (level) {
      case 'High': return 'text-green-400';
      case 'Medium': return 'text-yellow-400';
      case 'Low': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };
  return (
    <Link to={`/project/${id}`} className="block">
      <div className="bg-gray-800 rounded-lg overflow-hidden hover:ring-2 hover:ring-blue-500 transition-all group">
        <div className="relative">
          <img src={image} alt={title} className="w-full h-48 object-cover" />
          <div className="absolute top-3 left-3 flex gap-2">
            <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(difficulty)}`}>
              {difficulty}
            </span>
            <span className="px-2 py-1 text-xs rounded-full bg-blue-600 text-white">
              {language}
            </span>
          </div>
          {issues > 0 && (
            <div className="absolute top-3 right-3">
              <span className="flex items-center px-2 py-1 text-xs rounded-full bg-orange-600 text-white">
                <AlertCircle className="h-3 w-3 mr-1" />
                {issues}
              </span>
            </div>
          )}
        </div>

        <div className="p-4">
          <div className="flex items-start justify-between mb-2">
            <h3 className="text-lg font-semibold text-gray-100 group-hover:text-blue-400 transition-colors">
              {title}
            </h3>
            <span className={`text-xs ${getActivityColor(activityLevel)}`}>
              ● {activityLevel}
            </span>
          </div>

          <p className="text-sm text-gray-400 mb-3 line-clamp-2">{description}</p>

          <div className="text-xs text-gray-500 mb-3">
            <span className="font-medium text-gray-400">{domain}</span>
            <span className="mx-2">•</span>
            <span>Updated {formatDate(lastUpdated)}</span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-gray-400">
              <div className="flex items-center">
                <Star className="h-4 w-4 mr-1" />
                <span className="text-sm">{stars}</span>
              </div>
              <div className="flex items-center">
                <GitBranch className="h-4 w-4 mr-1" />
                <span className="text-sm">{forks}</span>
              </div>
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-1" />
                <span className="text-sm">{contributors}</span>
              </div>
            </div>
          </div>

          <div className="mt-3 flex flex-wrap gap-1">
            {tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 text-xs rounded bg-gray-700 text-gray-300"
              >
                {tag}
              </span>
            ))}
            {tags.length > 3 && (
              <span className="px-2 py-1 text-xs rounded bg-gray-700 text-gray-400">
                +{tags.length - 3}
              </span>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}
