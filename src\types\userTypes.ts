export interface User {
  id: string;
  githubId: number;
  username: string;
  name: string;
  email: string;
  avatar: string;
  bio?: string;
  location?: string;
  website?: string;
  githubUrl: string;
  joinedAt: string;
  lastActive: string;
  isPublic: boolean;
  stats: UserStats;
  preferences: UserPreferences;
}

export interface UserStats {
  totalContributions: number;
  projectsViewed: number;
  projectsBookmarked: number;
  prsSubmitted: number;
  issuesOpened: number;
  commentsPosted: number;
  streakDays: number;
  longestStreak: number;
  totalPoints: number;
  level: number;
}

export interface UserPreferences {
  theme: 'dark' | 'light' | 'system';
  emailNotifications: boolean;
  publicProfile: boolean;
  showActivity: boolean;
  preferredLanguages: string[];
  interestedDomains: string[];
}

export interface Contribution {
  id: string;
  userId: string;
  type: 'view' | 'bookmark' | 'pr' | 'issue' | 'comment' | 'star';
  projectId: string;
  projectTitle: string;
  projectUrl?: string;
  description?: string;
  timestamp: string;
  points: number;
  metadata?: {
    prUrl?: string;
    issueUrl?: string;
    prStatus?: 'open' | 'merged' | 'closed';
    issueStatus?: 'open' | 'closed';
    language?: string;
    difficulty?: string;
  };
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  category: 'contribution' | 'engagement' | 'milestone' | 'special';
  requirement: {
    type: 'count' | 'streak' | 'special';
    target: number;
    metric: 'prs' | 'views' | 'bookmarks' | 'days' | 'projects' | 'points';
  };
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  unlockedAt?: string;
}

export interface UserBadge {
  badgeId: string;
  userId: string;
  unlockedAt: string;
  progress?: number;
}

export interface BookmarkedProject {
  id: string;
  userId: string;
  projectId: string;
  bookmarkedAt: string;
  notes?: string;
  tags?: string[];
}

export interface ActivityFeed {
  id: string;
  userId: string;
  type: 'badge_earned' | 'pr_submitted' | 'project_bookmarked' | 'milestone_reached';
  title: string;
  description: string;
  timestamp: string;
  metadata?: {
    badgeId?: string;
    projectId?: string;
    prUrl?: string;
    milestone?: string;
  };
  isPublic: boolean;
}

// GitHub OAuth types
export interface GitHubUser {
  id: number;
  login: string;
  name: string;
  email: string;
  avatar_url: string;
  bio: string;
  location: string;
  blog: string;
  html_url: string;
  public_repos: number;
  followers: number;
  following: number;
  created_at: string;
}

export interface GitHubRepo {
  id: number;
  name: string;
  full_name: string;
  description: string;
  html_url: string;
  language: string;
  stargazers_count: number;
  forks_count: number;
  open_issues_count: number;
  created_at: string;
  updated_at: string;
}

// Authentication state
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

// API response types
export interface LoginResponse {
  user: User;
  token: string;
  expiresAt: string;
}

export interface ContributionResponse {
  contributions: Contribution[];
  totalCount: number;
  hasMore: boolean;
}

export interface BadgeResponse {
  badges: Badge[];
  userBadges: UserBadge[];
  availableBadges: Badge[];
}

// Points system
export const POINTS_SYSTEM = {
  PROJECT_VIEW: 1,
  PROJECT_BOOKMARK: 5,
  PR_SUBMITTED: 25,
  PR_MERGED: 50,
  ISSUE_OPENED: 10,
  ISSUE_RESOLVED: 20,
  COMMENT_POSTED: 2,
  PROJECT_STARRED: 3,
  DAILY_STREAK: 5,
  WEEKLY_STREAK: 25,
  MONTHLY_STREAK: 100,
} as const;

// Badge definitions
export const BADGE_DEFINITIONS: Badge[] = [
  {
    id: 'first-pr',
    name: 'First Contribution',
    description: 'Submitted your first pull request',
    icon: '🎉',
    color: 'bg-green-500',
    category: 'milestone',
    requirement: { type: 'count', target: 1, metric: 'prs' },
    rarity: 'common'
  },
  {
    id: 'pr-master',
    name: 'PR Master',
    description: 'Submitted 10 pull requests',
    icon: '🚀',
    color: 'bg-blue-500',
    category: 'contribution',
    requirement: { type: 'count', target: 10, metric: 'prs' },
    rarity: 'uncommon'
  },
  {
    id: 'pr-legend',
    name: 'PR Legend',
    description: 'Submitted 50 pull requests',
    icon: '⭐',
    color: 'bg-purple-500',
    category: 'contribution',
    requirement: { type: 'count', target: 50, metric: 'prs' },
    rarity: 'rare'
  },
  {
    id: 'explorer',
    name: 'Explorer',
    description: 'Viewed 100 projects',
    icon: '🔍',
    color: 'bg-yellow-500',
    category: 'engagement',
    requirement: { type: 'count', target: 100, metric: 'views' },
    rarity: 'common'
  },
  {
    id: 'curator',
    name: 'Curator',
    description: 'Bookmarked 25 projects',
    icon: '📚',
    color: 'bg-indigo-500',
    category: 'engagement',
    requirement: { type: 'count', target: 25, metric: 'bookmarks' },
    rarity: 'uncommon'
  },
  {
    id: 'streak-warrior',
    name: 'Streak Warrior',
    description: 'Maintained a 7-day activity streak',
    icon: '🔥',
    color: 'bg-orange-500',
    category: 'engagement',
    requirement: { type: 'streak', target: 7, metric: 'days' },
    rarity: 'uncommon'
  },
  {
    id: 'dedication',
    name: 'Dedication',
    description: 'Maintained a 30-day activity streak',
    icon: '💎',
    color: 'bg-cyan-500',
    category: 'engagement',
    requirement: { type: 'streak', target: 30, metric: 'days' },
    rarity: 'epic'
  },
  {
    id: 'point-collector',
    name: 'Point Collector',
    description: 'Earned 1000 points',
    icon: '💰',
    color: 'bg-emerald-500',
    category: 'milestone',
    requirement: { type: 'count', target: 1000, metric: 'points' },
    rarity: 'rare'
  },
  {
    id: 'early-adopter',
    name: 'Early Adopter',
    description: 'One of the first 100 users',
    icon: '🌟',
    color: 'bg-pink-500',
    category: 'special',
    requirement: { type: 'special', target: 100, metric: 'projects' },
    rarity: 'legendary'
  }
];

// Level system
export const LEVEL_THRESHOLDS = [
  0, 100, 250, 500, 1000, 2000, 3500, 5500, 8000, 12000, 17000, 25000, 35000, 50000, 75000
];

export function calculateLevel(points: number): number {
  for (let i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--) {
    if (points >= LEVEL_THRESHOLDS[i]) {
      return i + 1;
    }
  }
  return 1;
}

export function getPointsForNextLevel(currentPoints: number): number {
  const currentLevel = calculateLevel(currentPoints);
  if (currentLevel >= LEVEL_THRESHOLDS.length) {
    return 0; // Max level reached
  }
  return LEVEL_THRESHOLDS[currentLevel] - currentPoints;
}

export function getLevelProgress(points: number): number {
  const currentLevel = calculateLevel(points);
  if (currentLevel >= LEVEL_THRESHOLDS.length) {
    return 100; // Max level
  }
  
  const currentLevelThreshold = LEVEL_THRESHOLDS[currentLevel - 1];
  const nextLevelThreshold = LEVEL_THRESHOLDS[currentLevel];
  const progress = ((points - currentLevelThreshold) / (nextLevelThreshold - currentLevelThreshold)) * 100;
  
  return Math.min(100, Math.max(0, progress));
}
