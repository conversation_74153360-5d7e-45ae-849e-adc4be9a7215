import React, { useState, useCallback, useEffect } from 'react';
import { Search, Filter, X, SlidersHorizontal, Star, RefreshCw } from 'lucide-react';
import { ProjectFilters, Project } from '@/types/project.types';
import { Button, Input } from '@/components/ui';

interface ProjectSearchProps {
  onFiltersChange: (filters: ProjectFilters) => void;
  loading?: boolean;
  projects?: Project[]; // For extracting filter options
}

export function ProjectSearch({ onFiltersChange, loading = false, projects = [] }: ProjectSearchProps) {
  const [showFilters, setShowFilters] = useState(false);
  const [localFilters, setLocalFilters] = useState<ProjectFilters>({});

  // Extract unique values for filter options
  const languages = [...new Set(projects.map(p => p.language).filter(Boolean))].sort();
  const difficulties = ['Beginner', 'Intermediate', 'Advanced'];
  const domains = [...new Set(projects.map(p => p.domain).filter(Boolean))].sort();
  const activityLevels = ['Low', 'Medium', 'High'];

  // Debounced filter application
  const applyFilters = useCallback(() => {
    onFiltersChange(localFilters);
  }, [localFilters, onFiltersChange]);

  // Apply filters with debounce
  useEffect(() => {
    const timer = setTimeout(applyFilters, 300);
    return () => clearTimeout(timer);
  }, [applyFilters]);

  const updateFilter = useCallback((key: keyof ProjectFilters, value: any) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const toggleArrayFilter = useCallback((key: keyof ProjectFilters, value: string) => {
    setLocalFilters(prev => {
      const currentArray = (prev[key] as string[]) || [];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(v => v !== value)
        : [...currentArray, value];
      return { ...prev, [key]: newArray.length > 0 ? newArray : undefined };
    });
  }, []);

  const clearFilters = useCallback(() => {
    setLocalFilters({});
  }, []);

  const activeFiltersCount = Object.keys(localFilters).filter(key => {
    const value = localFilters[key as keyof ProjectFilters];
    return value !== undefined && value !== '' && (Array.isArray(value) ? value.length > 0 : true);
  }).length;

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <input
          type="text"
          placeholder="Search projects, technologies, keywords..."
          value={localFilters.query || ''}
          onChange={(e) => updateFilter('query', e.target.value || undefined)}
          className="w-full bg-gray-800 rounded-lg pl-10 pr-4 py-3 text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 border border-gray-700"
        />
        {loading && (
          <RefreshCw className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-blue-500 animate-spin" />
        )}
      </div>

      {/* Filter Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-4">
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`flex items-center justify-center sm:justify-start space-x-2 px-4 py-2 rounded-lg border transition-colors ${
            showFilters
              ? 'bg-blue-600 border-blue-500 text-white'
              : 'bg-gray-800 border-gray-700 text-gray-300 hover:bg-gray-700'
          }`}
        >
          <SlidersHorizontal className="h-4 w-4" />
          <span>Filters</span>
          {activeFiltersCount > 0 && (
            <span className="bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
              {activeFiltersCount}
            </span>
          )}
        </button>

        {/* Clear Filters */}
        {activeFiltersCount > 0 && (
          <button
            onClick={clearFilters}
            className="flex items-center justify-center space-x-1 px-3 py-2 text-sm text-red-400 hover:text-red-300 transition-colors border border-red-400/30 rounded-lg hover:bg-red-400/10"
          >
            <X className="h-3 w-3" />
            <span>Clear all filters</span>
          </button>
        )}

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => updateFilter('hasGoodFirstIssues', !localFilters.hasGoodFirstIssues)}
            className={`px-3 py-1 text-sm rounded-full transition-colors ${
              localFilters.hasGoodFirstIssues
                ? 'bg-green-600 text-white'
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700 border border-gray-600'
            }`}
          >
            Good First Issues
          </button>
          <button
            onClick={() => updateFilter('minStars', localFilters.minStars ? undefined : 100)}
            className={`px-3 py-1 text-sm rounded-full transition-colors ${
              localFilters.minStars
                ? 'bg-yellow-600 text-white'
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700 border border-gray-600'
            }`}
          >
            <Star className="h-3 w-3 inline mr-1" />
            Popular (100+ stars)
          </button>
        </div>
      </div>

      {/* Advanced Filters Panel */}
      {showFilters && (
        <div className="bg-gray-800 rounded-lg p-4 sm:p-6 border border-gray-700 animate-in slide-in-from-top-2 duration-200">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            
            {/* Languages Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-300 mb-3">Languages</h3>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {languages.map(language => (
                  <label key={language} className="flex items-center space-x-2 cursor-pointer hover:bg-gray-700/50 p-1 rounded">
                    <input
                      type="checkbox"
                      checked={(localFilters.languages || []).includes(language)}
                      onChange={() => toggleArrayFilter('languages', language)}
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                    />
                    <span className="text-gray-300 text-sm">{language}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Difficulty Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-300 mb-3">Difficulty</h3>
              <div className="space-y-2">
                {difficulties.map(difficulty => (
                  <label key={difficulty} className="flex items-center space-x-2 cursor-pointer hover:bg-gray-700/50 p-1 rounded">
                    <input
                      type="checkbox"
                      checked={(localFilters.difficulties || []).includes(difficulty as any)}
                      onChange={() => toggleArrayFilter('difficulties', difficulty)}
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                    />
                    <span className={`text-sm ${
                      difficulty === 'Beginner' ? 'text-green-400' :
                      difficulty === 'Intermediate' ? 'text-yellow-400' : 'text-red-400'
                    }`}>
                      {difficulty}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Domain Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-300 mb-3">Domain</h3>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {domains.map(domain => (
                  <label key={domain} className="flex items-center space-x-2 cursor-pointer hover:bg-gray-700/50 p-1 rounded">
                    <input
                      type="checkbox"
                      checked={(localFilters.domains || []).includes(domain)}
                      onChange={() => toggleArrayFilter('domains', domain)}
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                    />
                    <span className="text-gray-300 text-sm">{domain}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Activity Level Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-300 mb-3">Activity</h3>
              <div className="space-y-2">
                {activityLevels.map(level => (
                  <label key={level} className="flex items-center space-x-2 cursor-pointer hover:bg-gray-700/50 p-1 rounded">
                    <input
                      type="checkbox"
                      checked={(localFilters.activityLevels || []).includes(level as any)}
                      onChange={() => toggleArrayFilter('activityLevels', level)}
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                    />
                    <span className={`text-sm ${
                      level === 'High' ? 'text-green-400' :
                      level === 'Medium' ? 'text-yellow-400' : 'text-gray-400'
                    }`}>
                      {level}
                    </span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Star Range */}
          <div className="mt-6 pt-4 border-t border-gray-700">
            <h3 className="text-sm font-medium text-gray-300 mb-3">Minimum Stars</h3>
            <div className="flex items-center space-x-4">
              <input
                type="range"
                min="0"
                max="1000"
                step="10"
                value={localFilters.minStars || 0}
                onChange={(e) => updateFilter('minStars', parseInt(e.target.value) || undefined)}
                className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <span className="text-sm text-gray-300 min-w-[60px]">
                {localFilters.minStars || 0}+ stars
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
