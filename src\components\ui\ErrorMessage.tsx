import React from 'react';
import { AlertCircle, RefreshCw, X } from 'lucide-react';
import { Button } from './Button';

export interface ErrorMessageProps {
  title?: string;
  message: string;
  variant?: 'error' | 'warning' | 'info';
  showIcon?: boolean;
  onRetry?: () => void;
  onDismiss?: () => void;
  retryText?: string;
  className?: string;
}

const variantClasses = {
  error: {
    container: 'bg-red-900/20 border-red-500/30 text-red-200',
    icon: 'text-red-400',
    title: 'text-red-300',
  },
  warning: {
    container: 'bg-yellow-900/20 border-yellow-500/30 text-yellow-200',
    icon: 'text-yellow-400',
    title: 'text-yellow-300',
  },
  info: {
    container: 'bg-blue-900/20 border-blue-500/30 text-blue-200',
    icon: 'text-blue-400',
    title: 'text-blue-300',
  },
};

export function ErrorMessage({
  title,
  message,
  variant = 'error',
  showIcon = true,
  onRetry,
  onDismiss,
  retryText = 'Try Again',
  className = '',
}: ErrorMessageProps) {
  const classes = variantClasses[variant];

  return (
    <div className={`rounded-lg border p-4 ${classes.container} ${className}`}>
      <div className="flex items-start">
        {showIcon && (
          <div className="flex-shrink-0">
            <AlertCircle className={`h-5 w-5 ${classes.icon}`} />
          </div>
        )}
        
        <div className={`${showIcon ? 'ml-3' : ''} flex-1`}>
          {title && (
            <h3 className={`text-sm font-medium ${classes.title} mb-1`}>
              {title}
            </h3>
          )}
          <p className="text-sm">{message}</p>
          
          {(onRetry || onDismiss) && (
            <div className="mt-3 flex space-x-2">
              {onRetry && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRetry}
                  icon={RefreshCw}
                  className="text-xs"
                >
                  {retryText}
                </Button>
              )}
              {onDismiss && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onDismiss}
                  icon={X}
                  className="text-xs"
                >
                  Dismiss
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Centered error message for full page errors
export function CenteredErrorMessage({
  title = 'Something went wrong',
  message,
  onRetry,
  retryText = 'Try Again',
  className = '',
}: Omit<ErrorMessageProps, 'variant' | 'showIcon' | 'onDismiss'>) {
  return (
    <div className={`flex flex-col items-center justify-center py-12 text-center ${className}`}>
      <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6 max-w-md">
        <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-red-300 mb-2">{title}</h3>
        <p className="text-sm text-red-200 mb-4">{message}</p>
        {onRetry && (
          <Button
            variant="outline"
            onClick={onRetry}
            icon={RefreshCw}
            className="text-red-300 border-red-500/30 hover:bg-red-900/30"
          >
            {retryText}
          </Button>
        )}
      </div>
    </div>
  );
}

// Inline error message for forms and small spaces
export function InlineErrorMessage({
  message,
  className = '',
}: Pick<ErrorMessageProps, 'message' | 'className'>) {
  return (
    <div className={`flex items-center text-red-400 text-sm ${className}`}>
      <AlertCircle className="h-4 w-4 mr-1 flex-shrink-0" />
      <span>{message}</span>
    </div>
  );
}
