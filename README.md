# IDXCodeHub

A modern platform for discovering and contributing to open source projects with gamification features and comprehensive project insights.

## 🚀 Features

- **Project Discovery**: Advanced search and filtering for open source projects
- **User Profiles**: GitHub OAuth authentication with contribution tracking
- **Gamification**: Badge system, levels, and achievement tracking
- **Project Details**: Comprehensive project information with contribution guides
- **Bookmarking**: Save and organize favorite projects
- **Responsive Design**: Mobile-first design with dark theme

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Basic UI components (Button, Input, Badge)
│   ├── layout/          # Layout components (Header, Footer, Navigation)
│   ├── features/        # Feature-specific components
│   │   ├── auth/        # Authentication components
│   │   ├── projects/    # Project-related components
│   │   ├── profile/     # User profile components
│   │   └── search/      # Search and filter components
│   └── common/          # Shared components
├── pages/               # Page components (routes)
├── hooks/               # Custom React hooks
├── contexts/            # React contexts (Auth, Theme, etc.)
├── services/            # API and external services
├── utils/               # Utility functions and helpers
├── types/               # TypeScript type definitions
├── constants/           # Application constants and data
└── styles/              # Global styles and themes
```

## 🛠️ Technology Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Build Tool**: Vite
- **Routing**: React Router v6
- **State Management**: React Context + useReducer
- **Icons**: Lucide React
- **Authentication**: GitHub OAuth
- **Storage**: LocalStorage (demo mode)

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/idxcodehub.git
   cd idxcodehub
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`
