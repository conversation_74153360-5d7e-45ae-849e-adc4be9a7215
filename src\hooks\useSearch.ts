import { useState, useEffect, useMemo } from 'react';
import { Project } from '../types/project.types';
import { PROJECTS_DATA } from '../constants/projects.data';
import { SEARCH_CONFIG } from '../constants/app.constants';

export interface SearchFilters {
  query: string;
  languages: string[];
  difficulties: string[];
  domains: string[];
  activityLevels: string[];
  sortBy: 'stars' | 'forks' | 'updated' | 'name' | 'contributors';
  sortOrder: 'asc' | 'desc';
}

export interface SearchResult {
  projects: Project[];
  totalCount: number;
  hasMore: boolean;
  isLoading: boolean;
  error: string | null;
}

const initialFilters: SearchFilters = {
  query: '',
  languages: [],
  difficulties: [],
  domains: [],
  activityLevels: [],
  sortBy: 'stars',
  sortOrder: 'desc',
};

export function useSearch() {
  const [filters, setFilters] = useState<SearchFilters>(initialFilters);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Debounced search query
  const [debouncedQuery, setDebouncedQuery] = useState(filters.query);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(filters.query);
    }, SEARCH_CONFIG.debounceDelay);

    return () => clearTimeout(timer);
  }, [filters.query]);

  // Perform search and filtering
  const searchResults = useMemo(() => {
    setIsLoading(true);
    setError(null);

    try {
      let filteredProjects = [...PROJECTS_DATA];

      // Text search
      if (debouncedQuery && debouncedQuery.length >= SEARCH_CONFIG.minQueryLength) {
        const query = debouncedQuery.toLowerCase();
        filteredProjects = filteredProjects.filter(project =>
          project.title.toLowerCase().includes(query) ||
          project.description.toLowerCase().includes(query) ||
          project.tags.some(tag => tag.toLowerCase().includes(query)) ||
          project.keywords.some(keyword => keyword.toLowerCase().includes(query))
        );
      }

      // Language filter
      if (filters.languages.length > 0) {
        filteredProjects = filteredProjects.filter(project =>
          filters.languages.includes(project.language)
        );
      }

      // Difficulty filter
      if (filters.difficulties.length > 0) {
        filteredProjects = filteredProjects.filter(project =>
          filters.difficulties.includes(project.difficulty)
        );
      }

      // Domain filter
      if (filters.domains.length > 0) {
        filteredProjects = filteredProjects.filter(project =>
          filters.domains.includes(project.domain)
        );
      }

      // Activity level filter
      if (filters.activityLevels.length > 0) {
        filteredProjects = filteredProjects.filter(project =>
          filters.activityLevels.includes(project.activityLevel)
        );
      }

      // Sorting
      filteredProjects.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (filters.sortBy) {
          case 'stars':
            aValue = a.stars;
            bValue = b.stars;
            break;
          case 'forks':
            aValue = a.forks;
            bValue = b.forks;
            break;
          case 'updated':
            aValue = new Date(a.lastUpdated);
            bValue = new Date(b.lastUpdated);
            break;
          case 'name':
            aValue = a.title.toLowerCase();
            bValue = b.title.toLowerCase();
            break;
          case 'contributors':
            aValue = a.contributors;
            bValue = b.contributors;
            break;
          default:
            aValue = a.stars;
            bValue = b.stars;
        }

        if (filters.sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });

      const result: SearchResult = {
        projects: filteredProjects.slice(0, SEARCH_CONFIG.maxResults),
        totalCount: filteredProjects.length,
        hasMore: filteredProjects.length > SEARCH_CONFIG.maxResults,
        isLoading: false,
        error: null,
      };

      setIsLoading(false);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Search failed';
      setError(errorMessage);
      setIsLoading(false);
      
      return {
        projects: [],
        totalCount: 0,
        hasMore: false,
        isLoading: false,
        error: errorMessage,
      };
    }
  }, [debouncedQuery, filters]);

  // Update filters
  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Reset filters
  const resetFilters = () => {
    setFilters(initialFilters);
  };

  // Clear search
  const clearSearch = () => {
    setFilters(prev => ({ ...prev, query: '' }));
  };

  // Toggle filter value
  const toggleFilter = (filterType: keyof Pick<SearchFilters, 'languages' | 'difficulties' | 'domains' | 'activityLevels'>, value: string) => {
    setFilters(prev => {
      const currentValues = prev[filterType];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      
      return { ...prev, [filterType]: newValues };
    });
  };

  return {
    filters,
    searchResults,
    updateFilters,
    resetFilters,
    clearSearch,
    toggleFilter,
    isLoading,
    error,
  };
}
