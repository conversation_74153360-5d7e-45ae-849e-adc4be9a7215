import React, { useState, useEffect } from 'react';
import { AlertTriangle, Clock, Github, ExternalLink } from 'lucide-react';

interface RateLimitInfo {
  limit: number;
  used: number;
  remaining: number;
  resetTime: Date;
  hasToken: boolean;
}

export function GitHubRateLimitWarning() {
  const [rateLimitInfo, setRateLimitInfo] = useState<RateLimitInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeUntilReset, setTimeUntilReset] = useState<string>('');

  useEffect(() => {
    checkRateLimit();
  }, []);

  useEffect(() => {
    if (rateLimitInfo?.resetTime) {
      const interval = setInterval(() => {
        const now = new Date();
        const resetTime = rateLimitInfo.resetTime;
        const diff = resetTime.getTime() - now.getTime();
        
        if (diff <= 0) {
          setTimeUntilReset('Rate limit has reset!');
          // Refresh rate limit info
          checkRateLimit();
        } else {
          const minutes = Math.floor(diff / (1000 * 60));
          const seconds = Math.floor((diff % (1000 * 60)) / 1000);
          setTimeUntilReset(`${minutes}m ${seconds}s`);
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [rateLimitInfo]);

  const checkRateLimit = async () => {
    try {
      const headers: Record<string, string> = {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'IDXCodeHub/1.0.0',
      };

      if (import.meta.env.VITE_GITHUB_TOKEN) {
        headers['Authorization'] = `token ${import.meta.env.VITE_GITHUB_TOKEN}`;
      }

      const response = await fetch('https://api.github.com/rate_limit', { headers });
      
      if (response.ok) {
        const data = await response.json();
        const { core } = data.resources;
        
        setRateLimitInfo({
          limit: core.limit,
          used: core.used,
          remaining: core.remaining,
          resetTime: new Date(core.reset * 1000),
          hasToken: !!import.meta.env.VITE_GITHUB_TOKEN,
        });
      }
    } catch (error) {
      console.error('Failed to check rate limit:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return null;

  // Only show warning if rate limit is low or exceeded
  if (!rateLimitInfo || rateLimitInfo.remaining > 10) return null;

  const isExceeded = rateLimitInfo.remaining === 0;
  const isLow = rateLimitInfo.remaining <= 10 && rateLimitInfo.remaining > 0;

  return (
    <div className={`rounded-lg border p-4 mb-6 ${
      isExceeded 
        ? 'bg-red-900/20 border-red-500/30' 
        : 'bg-yellow-900/20 border-yellow-500/30'
    }`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {isExceeded ? (
            <AlertTriangle className="h-5 w-5 text-red-400" />
          ) : (
            <Clock className="h-5 w-5 text-yellow-400" />
          )}
        </div>
        
        <div className="flex-1">
          <h3 className={`text-sm font-medium mb-1 ${
            isExceeded ? 'text-red-300' : 'text-yellow-300'
          }`}>
            {isExceeded ? 'GitHub API Rate Limit Exceeded' : 'GitHub API Rate Limit Low'}
          </h3>
          
          <div className="text-sm text-gray-300 space-y-2">
            <div className="flex items-center justify-between">
              <span>Requests used:</span>
              <span className="font-mono">
                {rateLimitInfo.used}/{rateLimitInfo.limit}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span>Remaining:</span>
              <span className={`font-mono ${
                rateLimitInfo.remaining === 0 ? 'text-red-400' : 'text-yellow-400'
              }`}>
                {rateLimitInfo.remaining}
              </span>
            </div>
            
            {isExceeded && (
              <div className="flex items-center justify-between">
                <span>Resets in:</span>
                <span className="font-mono text-blue-400">{timeUntilReset}</span>
              </div>
            )}
          </div>
          
          {isExceeded && (
            <p className="text-sm text-gray-400 mt-2">
              {rateLimitInfo.hasToken 
                ? 'Your GitHub token may have expired or reached its limit.'
                : 'You can browse static projects or add a GitHub token for higher limits.'
              }
            </p>
          )}
          
          {!rateLimitInfo.hasToken && (
            <div className="mt-3 p-3 bg-gray-800 rounded border border-gray-700">
              <h4 className="text-sm font-medium text-gray-300 mb-2 flex items-center">
                <Github className="h-4 w-4 mr-2" />
                Get Higher Rate Limits
              </h4>
              <p className="text-xs text-gray-400 mb-2">
                Add a GitHub Personal Access Token to get 5,000 requests/hour instead of 60.
              </p>
              <div className="flex items-center space-x-2">
                <a
                  href="https://github.com/settings/tokens"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-xs bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded transition-colors"
                >
                  Create Token
                  <ExternalLink className="h-3 w-3 ml-1" />
                </a>
                <button
                  onClick={checkRateLimit}
                  className="text-xs bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded transition-colors"
                >
                  Refresh Status
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
