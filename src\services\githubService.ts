import { GitHubRepository, GitHubIssue, Project, ProjectFilters, ProjectSearchResult } from '@/types/project.types';

// GitHub API configuration
const GITHUB_API_BASE = 'https://api.github.com';
const GITHUB_TOKEN = import.meta.env.VITE_GITHUB_TOKEN; // Optional for higher rate limits

// Rate limiting configuration
const RATE_LIMIT_DELAY = 1000; // 1 second between requests
let lastRequestTime = 0;

class GitHubService {
  private async makeRequest<T>(url: string): Promise<T> {
    // Simple rate limiting
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
      await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY - timeSinceLastRequest));
    }
    lastRequestTime = Date.now();

    const headers: Record<string, string> = {
      'Accept': 'application/vnd.github.v3+json',
      'User-Agent': 'IDXCodeHub/1.0.0',
    };

    if (GITHUB_TOKEN) {
      headers['Authorization'] = `token ${GITHUB_TOKEN}`;
    }

    const response = await fetch(url, { headers });

    if (!response.ok) {
      if (response.status === 403) {
        throw new Error('GitHub API rate limit exceeded. Please try again later.');
      }
      throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async searchRepositories(filters: ProjectFilters = {}): Promise<ProjectSearchResult> {
    const {
      query = '',
      languages = [],
      minStars = 10,
      hasGoodFirstIssues = false,
    } = filters;

    // Build GitHub search query
    const searchTerms = [];
    
    if (query) {
      searchTerms.push(query);
    }

    // Add language filters
    if (languages.length > 0) {
      languages.forEach(lang => searchTerms.push(`language:${lang}`));
    }

    // Add star filter
    searchTerms.push(`stars:>=${minStars}`);

    // Add good first issues filter
    if (hasGoodFirstIssues) {
      searchTerms.push('good-first-issues:>0');
    }

    // Additional filters for quality projects
    searchTerms.push('archived:false');
    searchTerms.push('is:public');
    searchTerms.push('pushed:>2023-01-01'); // Active in the last year

    const searchQuery = searchTerms.join(' ');
    const url = `${GITHUB_API_BASE}/search/repositories?q=${encodeURIComponent(searchQuery)}&sort=stars&order=desc&per_page=30`;

    try {
      const response = await this.makeRequest<{
        total_count: number;
        incomplete_results: boolean;
        items: GitHubRepository[];
      }>(url);

      const projects = await Promise.all(
        response.items.map(repo => this.transformGitHubRepoToProject(repo))
      );

      return {
        projects: projects.filter(p => p !== null) as Project[],
        totalCount: response.total_count,
        hasMore: response.items.length === 30,
      };
    } catch (error) {
      console.error('Error fetching repositories from GitHub:', error);
      return {
        projects: [],
        totalCount: 0,
        hasMore: false,
      };
    }
  }

  async getTrendingRepositories(language?: string, since: 'daily' | 'weekly' | 'monthly' = 'weekly'): Promise<Project[]> {
    const dateMap = {
      daily: 1,
      weekly: 7,
      monthly: 30,
    };

    const daysAgo = dateMap[since];
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    const dateString = date.toISOString().split('T')[0];

    const searchTerms = [
      `created:>${dateString}`,
      'stars:>10',
      'archived:false',
      'is:public',
    ];

    if (language) {
      searchTerms.push(`language:${language}`);
    }

    const searchQuery = searchTerms.join(' ');
    const url = `${GITHUB_API_BASE}/search/repositories?q=${encodeURIComponent(searchQuery)}&sort=stars&order=desc&per_page=20`;

    try {
      const response = await this.makeRequest<{
        items: GitHubRepository[];
      }>(url);

      const projects = await Promise.all(
        response.items.map(repo => this.transformGitHubRepoToProject(repo))
      );

      return projects.filter(p => p !== null) as Project[];
    } catch (error) {
      console.error('Error fetching trending repositories:', error);
      return [];
    }
  }

  async getGoodFirstIssues(repoFullName: string): Promise<GitHubIssue[]> {
    const url = `${GITHUB_API_BASE}/repos/${repoFullName}/issues?labels=good%20first%20issue,beginner,help%20wanted&state=open&per_page=10`;

    try {
      return await this.makeRequest<GitHubIssue[]>(url);
    } catch (error) {
      console.error(`Error fetching good first issues for ${repoFullName}:`, error);
      return [];
    }
  }

  async getRepositoryDetails(repoFullName: string): Promise<Project | null> {
    try {
      // Fetch repository details
      const repoUrl = `${GITHUB_API_BASE}/repos/${repoFullName}`;
      const repo = await this.makeRequest<GitHubRepository>(repoUrl);

      // Fetch README
      const readmeUrl = `${GITHUB_API_BASE}/repos/${repoFullName}/readme`;
      let readme = '';
      try {
        const readmeResponse = await this.makeRequest<{ content: string; encoding: string }>(readmeUrl);
        if (readmeResponse.encoding === 'base64') {
          readme = atob(readmeResponse.content);
        }
      } catch (error) {
        console.warn(`Could not fetch README for ${repoFullName}`);
      }

      // Fetch contributors
      const contributorsUrl = `${GITHUB_API_BASE}/repos/${repoFullName}/contributors?per_page=10`;
      let contributors: any[] = [];
      try {
        contributors = await this.makeRequest<any[]>(contributorsUrl);
      } catch (error) {
        console.warn(`Could not fetch contributors for ${repoFullName}`);
      }

      // Fetch recent commits
      const commitsUrl = `${GITHUB_API_BASE}/repos/${repoFullName}/commits?per_page=10`;
      let recentCommits: any[] = [];
      try {
        recentCommits = await this.makeRequest<any[]>(commitsUrl);
      } catch (error) {
        console.warn(`Could not fetch commits for ${repoFullName}`);
      }

      // Get good first issues
      const goodFirstIssues = await this.getGoodFirstIssues(repoFullName);

      return this.transformGitHubRepoToProject(repo, {
        readme,
        contributors,
        recentCommits,
        goodFirstIssues,
      });
    } catch (error) {
      console.error(`Error fetching repository details for ${repoFullName}:`, error);
      return null;
    }
  }

  private async transformGitHubRepoToProject(
    repo: GitHubRepository,
    additionalData?: {
      readme?: string;
      contributors?: any[];
      recentCommits?: any[];
      goodFirstIssues?: GitHubIssue[];
    }
  ): Promise<Project | null> {
    try {
      // Get good first issues count (use provided data or fetch)
      const goodFirstIssues = additionalData?.goodFirstIssues || await this.getGoodFirstIssues(repo.full_name);

      // Determine difficulty based on various factors
      const difficulty = this.calculateDifficulty(repo, goodFirstIssues.length);

      // Determine activity level
      const activityLevel = this.calculateActivityLevel(repo);

      // Generate domain from topics or language
      const domain = this.determineDomain(repo.topics, repo.language);

      // Generate project image (you might want to use a service like GitHub's social preview)
      const image = `https://opengraph.githubassets.com/1/${repo.full_name}`;

      return {
        id: `github-${repo.id}`,
        title: repo.name,
        description: repo.description || 'No description available',
        image,
        stars: repo.stargazers_count,
        forks: repo.forks_count,
        tags: repo.topics || [],
        language: repo.language || 'Unknown',
        difficulty,
        domain,
        activityLevel,
        lastUpdated: repo.pushed_at,
        contributors: repo.subscribers_count || 0,
        issues: repo.open_issues_count,
        license: repo.license?.name || 'Unknown',
        keywords: repo.topics || [],
        repositoryUrl: repo.html_url,
        source: 'github',
        sourceId: repo.id.toString(),
        fetchedAt: new Date().toISOString(),
        isActive: !repo.archived && !repo.disabled,
        goodFirstIssues: goodFirstIssues.map(issue => ({
          id: issue.id.toString(),
          title: issue.title,
          description: issue.body || '',
          url: issue.html_url,
          labels: issue.labels.map(label => label.name),
          createdAt: issue.created_at,
          difficulty: 'Easy' as const,
          estimatedTime: 'Unknown',
          author: issue.user.login,
        })),
        readme: additionalData?.readme,
        maintainers: additionalData?.contributors?.map(contributor => ({
          id: contributor.id?.toString() || contributor.login,
          name: contributor.login,
          avatar: contributor.avatar_url,
          role: 'Contributor',
          githubUrl: contributor.html_url,
          contributions: contributor.contributions || 0,
        })),
        recentCommits: additionalData?.recentCommits?.map(commit => ({
          id: commit.sha,
          message: commit.commit.message,
          author: commit.commit.author.name,
          date: commit.commit.author.date,
          url: commit.html_url,
        })),
      };
    } catch (error) {
      console.error(`Error transforming repository ${repo.full_name}:`, error);
      return null;
    }
  }

  private calculateDifficulty(repo: GitHubRepository, goodFirstIssuesCount: number): 'Beginner' | 'Intermediate' | 'Advanced' {
    // Simple algorithm to determine difficulty
    const factors = {
      hasGoodFirstIssues: goodFirstIssuesCount > 0,
      isPopular: repo.stargazers_count > 1000,
      isComplex: repo.forks_count > 100,
      hasDocumentation: repo.has_wiki,
    };

    if (factors.hasGoodFirstIssues && !factors.isComplex) {
      return 'Beginner';
    }

    if (factors.isPopular && factors.isComplex) {
      return 'Advanced';
    }

    return 'Intermediate';
  }

  private calculateActivityLevel(repo: GitHubRepository): 'Low' | 'Medium' | 'High' {
    const lastPush = new Date(repo.pushed_at);
    const now = new Date();
    const daysSinceLastPush = (now.getTime() - lastPush.getTime()) / (1000 * 60 * 60 * 24);

    if (daysSinceLastPush <= 7) return 'High';
    if (daysSinceLastPush <= 30) return 'Medium';
    return 'Low';
  }

  private determineDomain(topics: string[], language: string | null): string {
    const domainMap: Record<string, string> = {
      'web': 'Web Development',
      'frontend': 'Web Development',
      'backend': 'Web Development',
      'mobile': 'Mobile Development',
      'android': 'Mobile Development',
      'ios': 'Mobile Development',
      'machine-learning': 'Machine Learning',
      'ai': 'Artificial Intelligence',
      'data-science': 'Data Science',
      'devops': 'DevOps',
      'blockchain': 'Blockchain',
      'game': 'Game Development',
      'cli': 'Command Line Tools',
      'api': 'API Development',
    };

    // Check topics first
    for (const topic of topics) {
      if (domainMap[topic]) {
        return domainMap[topic];
      }
    }

    // Fallback to language-based domain
    const languageDomainMap: Record<string, string> = {
      'JavaScript': 'Web Development',
      'TypeScript': 'Web Development',
      'Python': 'Data Science',
      'Java': 'Enterprise Development',
      'Go': 'System Programming',
      'Rust': 'System Programming',
      'Swift': 'Mobile Development',
      'Kotlin': 'Mobile Development',
      'C++': 'System Programming',
      'C#': 'Enterprise Development',
    };

    return language ? languageDomainMap[language] || 'Software Development' : 'Software Development';
  }
}

export const githubService = new GitHubService();
