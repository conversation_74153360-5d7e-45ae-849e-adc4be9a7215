import { User, Contribution, Badge, UserBadge, BookmarkedProject, BADGE_DEFINITIONS } from '../types/userTypes';

// Demo user data for testing
export const DEMO_USERS: User[] = [
  {
    id: 'user_12345',
    githubId: 12345,
    username: 'demo_user',
    name: 'Demo User',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
    bio: 'Full-stack developer passionate about open source',
    location: 'San Francisco, CA',
    website: 'https://demo-user.dev',
    githubUrl: 'https://github.com/demo_user',
    joinedAt: '2024-01-01T00:00:00Z',
    lastActive: new Date().toISOString(),
    isPublic: true,
    stats: {
      totalContributions: 42,
      projectsViewed: 156,
      projectsBookmarked: 12,
      prsSubmitted: 8,
      issuesOpened: 3,
      commentsPosted: 24,
      streakDays: 7,
      longestStreak: 15,
      totalPoints: 485,
      level: 3,
    },
    preferences: {
      theme: 'dark',
      emailNotifications: true,
      publicProfile: true,
      showActivity: true,
      preferredLanguages: ['TypeScript', 'Python', 'React'],
      interestedDomains: ['Web Development', 'AI/ML'],
    },
  },
  {
    id: 'user_67890',
    githubId: 67890,
    username: 'jane_dev',
    name: 'Jane Developer',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
    bio: 'Frontend engineer and UI/UX enthusiast',
    location: 'New York, NY',
    website: 'https://jane-dev.com',
    githubUrl: 'https://github.com/jane_dev',
    joinedAt: '2024-01-15T00:00:00Z',
    lastActive: new Date().toISOString(),
    isPublic: true,
    stats: {
      totalContributions: 28,
      projectsViewed: 89,
      projectsBookmarked: 8,
      prsSubmitted: 5,
      issuesOpened: 2,
      commentsPosted: 15,
      streakDays: 3,
      longestStreak: 12,
      totalPoints: 320,
      level: 2,
    },
    preferences: {
      theme: 'dark',
      emailNotifications: false,
      publicProfile: true,
      showActivity: true,
      preferredLanguages: ['JavaScript', 'Vue', 'CSS'],
      interestedDomains: ['Web Development', 'Design'],
    },
  },
];

// Demo contributions for testing
export const DEMO_CONTRIBUTIONS: Contribution[] = [
  {
    id: 'contrib_1',
    userId: 'user_12345',
    type: 'view',
    projectId: '1',
    projectTitle: 'React Dashboard',
    projectUrl: 'https://github.com/example/react-dashboard',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    points: 1,
  },
  {
    id: 'contrib_2',
    userId: 'user_12345',
    type: 'bookmark',
    projectId: '1',
    projectTitle: 'React Dashboard',
    projectUrl: 'https://github.com/example/react-dashboard',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
    points: 5,
  },
  {
    id: 'contrib_3',
    userId: 'user_12345',
    type: 'pr',
    projectId: '6',
    projectTitle: 'Machine Learning Toolkit',
    projectUrl: 'https://github.com/example/ml-toolkit',
    description: 'Added support for additional evaluation metrics',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    points: 25,
    metadata: {
      prUrl: 'https://github.com/example/ml-toolkit/pull/123',
      prStatus: 'open',
      language: 'Python',
      difficulty: 'Easy',
    },
  },
  {
    id: 'contrib_4',
    userId: 'user_12345',
    type: 'star',
    projectId: '2',
    projectTitle: 'Vue Components',
    projectUrl: 'https://github.com/example/vue-components',
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
    points: 3,
  },
  {
    id: 'contrib_5',
    userId: 'user_12345',
    type: 'issue',
    projectId: '3',
    projectTitle: 'Node.js API',
    projectUrl: 'https://github.com/example/nodejs-api',
    description: 'Reported bug in authentication middleware',
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    points: 10,
    metadata: {
      issueUrl: 'https://github.com/example/nodejs-api/issues/456',
      issueStatus: 'open',
      language: 'JavaScript',
    },
  },
];

// Demo user badges
export const DEMO_USER_BADGES: UserBadge[] = [
  {
    badgeId: 'first-pr',
    userId: 'user_12345',
    unlockedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
  },
  {
    badgeId: 'explorer',
    userId: 'user_12345',
    unlockedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
  },
  {
    badgeId: 'streak-warrior',
    userId: 'user_12345',
    unlockedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
  },
];

// Demo bookmarked projects
export const DEMO_BOOKMARKS: BookmarkedProject[] = [
  {
    id: 'bookmark_1',
    userId: 'user_12345',
    projectId: '1',
    bookmarkedAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
    notes: 'Great dashboard template for my next project',
    tags: ['react', 'dashboard', 'template'],
  },
  {
    id: 'bookmark_2',
    userId: 'user_12345',
    projectId: '6',
    bookmarkedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
    notes: 'Useful ML toolkit for data science projects',
    tags: ['python', 'machine-learning', 'toolkit'],
  },
];

// Function to initialize demo data in localStorage
export function initializeDemoData() {
  // Only initialize if no user data exists
  if (!localStorage.getItem('user_data')) {
    const demoUser = DEMO_USERS[0];

    // Set user data
    localStorage.setItem('user_data', JSON.stringify(demoUser));
    localStorage.setItem('auth_token', 'demo_token_12345');

    // Set contributions
    localStorage.setItem(`contributions_${demoUser.id}`, JSON.stringify(DEMO_CONTRIBUTIONS));

    // Set badges
    localStorage.setItem(`badges_${demoUser.id}`, JSON.stringify(DEMO_USER_BADGES));

    // Set bookmarks
    localStorage.setItem(`bookmarks_${demoUser.id}`, JSON.stringify(DEMO_BOOKMARKS));

    console.log('Demo data initialized for user:', demoUser.username);
  }
}

// Function to clear all demo data
export function clearDemoData() {
  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.startsWith('user_') ||
        key.startsWith('contributions_') ||
        key.startsWith('badges_') ||
        key.startsWith('bookmarks_') ||
        key.startsWith('activity_') ||
        key === 'auth_token') {
      localStorage.removeItem(key);
    }
  });
  console.log('Demo data cleared');
}

// Function to simulate earning a new badge
export function simulateEarnBadge(userId: string, badgeId: string) {
  const userBadges = JSON.parse(localStorage.getItem(`badges_${userId}`) || '[]');

  // Check if badge already earned
  if (userBadges.find((ub: UserBadge) => ub.badgeId === badgeId)) {
    console.log('Badge already earned:', badgeId);
    return;
  }

  const newBadge: UserBadge = {
    badgeId,
    userId,
    unlockedAt: new Date().toISOString(),
  };

  userBadges.push(newBadge);
  localStorage.setItem(`badges_${userId}`, JSON.stringify(userBadges));

  console.log('New badge earned:', badgeId);
}

// Function to get earned badges for display
export function getEarnedBadges(userId: string): Badge[] {
  const userBadges: UserBadge[] = JSON.parse(localStorage.getItem(`badges_${userId}`) || '[]');
  const earnedBadgeIds = userBadges.map(ub => ub.badgeId);

  return BADGE_DEFINITIONS.filter(badge => earnedBadgeIds.includes(badge.id));
}
