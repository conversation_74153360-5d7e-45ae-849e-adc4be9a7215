import React from "react";
import { Link } from "react-router-dom";
import { docs } from "../utils/docs";

export function Documentation() {


  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Documentation</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {docs.map(({ title, icon: Icon, sections }) => (
          <div key={title} className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Icon className="h-6 w-6 text-blue-500" />
              <h2 className="text-xl font-semibold">{title}</h2>
            </div>
            <ul className="space-y-2">
              {sections.map((section) => (
                <li key={section}>
                  {title === "Open Source Guide" ? (
                    <Link
                      to="/open-source-guide"
                      className="block text-gray-400 hover:text-gray-100 hover:bg-gray-700 rounded px-3 py-2 transition-colors"
                    >
                      {section}
                    </Link>
                  ) : (
                    <a
                      href="#"
                      className="block text-gray-400 hover:text-gray-100 hover:bg-gray-700 rounded px-3 py-2 transition-colors"
                    >
                      {section}
                    </a>
                  )}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
      <div className="mt-12 bg-gray-800 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Need Help?</h2>
        <p className="text-gray-400 mb-4">
          Can't find what you're looking for? Check out these resources:
        </p>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <a
            href="#"
            className="block bg-gray-700 hover:bg-gray-600 rounded-lg p-4 transition-colors"
          >
            <h3 className="font-medium mb-2">Community Forums</h3>
            <p className="text-sm text-gray-400">
              Ask questions and get help from the community
            </p>
          </a>
          <a
            href="#"
            className="block bg-gray-700 hover:bg-gray-600 rounded-lg p-4 transition-colors"
          >
            <h3 className="font-medium mb-2">GitHub Issues</h3>
            <p className="text-sm text-gray-400">
              Report bugs or request new features
            </p>
          </a>
        </div>
      </div>
    </div>
  );
}
