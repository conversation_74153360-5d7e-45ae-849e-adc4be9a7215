import React, { useEffect, useState } from "react";
import { Home } from "lucide-react";
import { Link } from "react-router-dom";

const Errorpage = () => {
  const [text, setText] = useState("");
  const fullText = "404_ERROR::PAGE_NOT_FOUND";
  useEffect(() => {
    let currentIndex = 0;
    const intervalId = setInterval(() => {
      if (currentIndex <= fullText.length) {
        setText(fullText.slice(0, currentIndex));
        currentIndex++;
      } else {
        clearInterval(intervalId);
      }
    }, 100);
    return () => clearInterval(intervalId);
  }, []);

  return (
    <main className="w-full min-h-screen bg-gray-900 text-gray-100 flex flex-col items-center justify-center p-4">
      <div className="relative">
        <div className="animate-float">
          <div className="text-6xl md:text-8xl font-mono mb-8 relative">
            <span className="text-blue-400 absolute -left-8 animate-bracket-left">{`{`}</span>
            <span className="text-red-400">{text}</span>
            <span className="text-blue-400 absolute -right-8 animate-bracket-right">{`}`}</span>
          </div>
        </div>
        <p className="text-gray-400 text-center mb-8 font-mono">
          The requested resource could not be found on this server
        </p>
        <div className="flex justify-center">
          <button
            className="flex items-center gap-2 px-6 py-3 bg-blue-500 hover:bg-blue-600 rounded-lg transition-colors"
            onClick={() => window.location.reload()}
          >
            <Home size={20} />
            <Link
            to="/"
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg font-medium flex items-center"
          >
            Home 
          </Link>
          </button>
        </div>
        <div className="absolute -z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
          <div className="w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
        </div>
      </div>
     
    </main>
  );
};

export default Errorpage;
