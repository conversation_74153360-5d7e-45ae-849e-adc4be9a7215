import { Project } from '../types/project.types';

export const PROJECTS_DATA: Project[] = [
  {
    id: "1",
    title: "React Dashboard",
    description: "A modern dashboard template with dark mode and customizable components",
    image: "https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    stars: 245,
    forks: 57,
    tags: ["React", "TypeScript", "Tailwind"],
    language: "TypeScript",
    difficulty: "Intermediate",
    domain: "Web Development",
    activityLevel: "High",
    lastUpdated: "2024-01-15",
    contributors: 12,
    issues: 8,
    license: "MIT",
    keywords: ["dashboard", "admin", "template", "dark-mode", "responsive"],
    longDescription: "A comprehensive React dashboard template built with modern technologies. Features include customizable components, dark/light mode toggle, responsive design, and a clean, professional interface perfect for admin panels and data visualization applications.",
    repositoryUrl: "https://github.com/example/react-dashboard",
    demoUrl: "https://react-dashboard-demo.vercel.app",
    documentationUrl: "https://react-dashboard-docs.vercel.app",
    technologies: ["React 18", "TypeScript", "Tailwind CSS", "Vite", "React Router", "Recharts", "Framer Motion"],
    prerequisites: ["Node.js 16+", "npm or yarn", "Basic React knowledge"],
    setupInstructions: `## Getting Started

1. Clone the repository:
\`\`\`bash
git clone https://github.com/example/react-dashboard.git
cd react-dashboard
\`\`\`

2. Install dependencies:
\`\`\`bash
npm install
\`\`\`

3. Start the development server:
\`\`\`bash
npm run dev
\`\`\`

4. Open http://localhost:3000 in your browser`,
    contributionGuide: `## Contributing to React Dashboard

We welcome contributions! Here's how you can help:

### Getting Started
1. Fork the repository
2. Create a feature branch: \`git checkout -b feature/amazing-feature\`
3. Make your changes
4. Run tests: \`npm test\`
5. Commit your changes: \`git commit -m 'Add amazing feature'\`
6. Push to the branch: \`git push origin feature/amazing-feature\`
7. Open a Pull Request

### What We're Looking For
- Bug fixes
- New dashboard components
- Performance improvements
- Documentation updates
- Accessibility improvements

### Code Style
- Use TypeScript for all new code
- Follow the existing code style
- Add tests for new features
- Update documentation as needed`,
    goodFirstIssues: [
      {
        id: "issue-1",
        title: "Add dark mode toggle animation",
        description: "Improve the dark mode toggle with a smooth animation transition. Currently it's instant, but a fade or slide animation would enhance UX.",
        labels: ["good first issue", "enhancement", "ui"],
        difficulty: "Easy",
        estimatedTime: "2-3 hours",
        url: "https://github.com/example/react-dashboard/issues/23",
        createdAt: "2024-01-10",
        author: "maintainer1"
      },
      {
        id: "issue-2",
        title: "Fix responsive layout on mobile devices",
        description: "The sidebar doesn't collapse properly on mobile screens smaller than 768px. Need to implement a mobile-friendly navigation.",
        labels: ["good first issue", "bug", "responsive"],
        difficulty: "Medium",
        estimatedTime: "4-6 hours",
        url: "https://github.com/example/react-dashboard/issues/31",
        createdAt: "2024-01-12",
        author: "user123"
      }
    ],
    maintainers: [
      {
        id: "maintainer1",
        name: "Alex Johnson",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150",
        role: "Lead Maintainer",
        githubUrl: "https://github.com/alexjohnson",
        contributions: 156
      }
    ],
    recentCommits: [
      {
        id: "commit1",
        message: "feat: add new chart component with customizable colors",
        author: "Alex Johnson",
        date: "2024-01-15",
        url: "https://github.com/example/react-dashboard/commit/abc123"
      }
    ],
    readme: `# React Dashboard

A modern, responsive dashboard template built with React, TypeScript, and Tailwind CSS.

## Features

- 🌙 Dark/Light mode toggle
- 📱 Fully responsive design
- 📊 Interactive charts and graphs
- 🎨 Customizable components
- ⚡ Fast performance with Vite
- 🔧 TypeScript for better development experience

## Quick Start

\`\`\`bash
npm install
npm run dev
\`\`\`

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## License

MIT License - see [LICENSE](LICENSE) for details.`
  },
  {
    id: "2",
    title: "Vue Components Library",
    description: "A collection of reusable Vue.js components with TypeScript support",
    image: "https://images.unsplash.com/photo-**********-4365d14bab8c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    stars: 189,
    forks: 34,
    tags: ["Vue", "TypeScript", "Components"],
    language: "Vue",
    difficulty: "Beginner",
    domain: "Web Development",
    activityLevel: "Medium",
    lastUpdated: "2024-01-18",
    contributors: 8,
    issues: 5,
    license: "MIT",
    keywords: ["vue", "components", "library", "typescript", "ui"],
    longDescription: "A comprehensive Vue.js component library built with TypeScript. Features modern, accessible components that are easy to integrate into any Vue application. Perfect for developers looking to build consistent UIs quickly.",
    repositoryUrl: "https://github.com/example/vue-components",
    demoUrl: "https://vue-components-demo.vercel.app",
    documentationUrl: "https://vue-components-docs.vercel.app",
    technologies: ["Vue 3", "TypeScript", "Vite", "Vitest", "Storybook", "CSS3"],
    prerequisites: ["Node.js 16+", "Vue 3 knowledge", "TypeScript basics"],
    setupInstructions: `## Getting Started

1. Clone the repository:
\`\`\`bash
git clone https://github.com/example/vue-components.git
cd vue-components
\`\`\`

2. Install dependencies:
\`\`\`bash
npm install
\`\`\`

3. Start development:
\`\`\`bash
npm run dev
\`\`\`

4. View Storybook:
\`\`\`bash
npm run storybook
\`\`\``,
    contributionGuide: `## Contributing to Vue Components Library

We welcome contributions! Here's how you can help:

### Getting Started
1. Fork the repository
2. Create a feature branch: \`git checkout -b feature/new-component\`
3. Make your changes
4. Run tests: \`npm test\`
5. Commit your changes: \`git commit -m 'Add new component'\`
6. Push to the branch: \`git push origin feature/new-component\`
7. Open a Pull Request

### Component Guidelines
- Follow Vue 3 Composition API patterns
- Include TypeScript types
- Add Storybook stories
- Write unit tests
- Update documentation`,
    goodFirstIssues: [
      {
        id: "vue-issue-1",
        title: "Add loading state to Button component",
        description: "The Button component needs a loading state with spinner animation. Should disable the button and show loading indicator.",
        labels: ["good first issue", "enhancement", "component"],
        difficulty: "Easy",
        estimatedTime: "2-3 hours",
        url: "https://github.com/example/vue-components/issues/15",
        createdAt: "2024-01-15",
        author: "maintainer2"
      }
    ],
    maintainers: [
      {
        id: "maintainer2",
        name: "Sarah Chen",
        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150",
        role: "Lead Developer",
        githubUrl: "https://github.com/sarahchen",
        contributions: 89
      }
    ],
    recentCommits: [
      {
        id: "vue-commit1",
        message: "feat: add new Modal component with accessibility features",
        author: "Sarah Chen",
        date: "2024-01-18",
        url: "https://github.com/example/vue-components/commit/def456"
      }
    ],
    readme: `# Vue Components Library

A modern, accessible component library for Vue 3 applications.

## Features

- 🎨 Beautiful, customizable components
- ♿ Accessibility-first design
- 📱 Responsive and mobile-friendly
- 🔧 TypeScript support
- 📚 Comprehensive Storybook documentation
- ⚡ Tree-shakeable imports

## Installation

\`\`\`bash
npm install @example/vue-components
\`\`\`

## Usage

\`\`\`vue
<template>
  <Button variant="primary" @click="handleClick">
    Click me!
  </Button>
</template>

<script setup>
import { Button } from '@example/vue-components'
</script>
\`\`\`

## License

MIT License - see [LICENSE](LICENSE) for details.`
  },
  {
    id: "3",
    title: "Node.js API Boilerplate",
    description: "Production-ready Node.js API with authentication, validation, and testing",
    image: "https://images.unsplash.com/photo-**********-ef010cbdcc31?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    stars: 312,
    forks: 89,
    tags: ["Node.js", "Express", "MongoDB"],
    language: "JavaScript",
    difficulty: "Advanced",
    domain: "Backend",
    activityLevel: "High",
    lastUpdated: "2024-01-20",
    contributors: 15,
    issues: 12,
    license: "MIT",
    keywords: ["nodejs", "api", "express", "mongodb", "authentication"],
    longDescription: "A robust, production-ready Node.js API boilerplate with built-in authentication, input validation, error handling, logging, and comprehensive testing. Perfect for building scalable backend services with modern best practices.",
    repositoryUrl: "https://github.com/example/nodejs-api-boilerplate",
    demoUrl: "https://nodejs-api-demo.herokuapp.com",
    documentationUrl: "https://nodejs-api-docs.vercel.app",
    technologies: ["Node.js", "Express.js", "MongoDB", "Mongoose", "JWT", "Joi", "Jest", "Winston", "Helmet"],
    prerequisites: ["Node.js 18+", "MongoDB", "Basic JavaScript knowledge", "REST API concepts"],
    setupInstructions: `## Getting Started

1. Clone the repository:
\`\`\`bash
git clone https://github.com/example/nodejs-api-boilerplate.git
cd nodejs-api-boilerplate
\`\`\`

2. Install dependencies:
\`\`\`bash
npm install
\`\`\`

3. Set up environment variables:
\`\`\`bash
cp .env.example .env
# Edit .env with your configuration
\`\`\`

4. Start MongoDB (if running locally):
\`\`\`bash
mongod
\`\`\`

5. Run the application:
\`\`\`bash
npm run dev
\`\`\`

6. Run tests:
\`\`\`bash
npm test
\`\`\``,
    contributionGuide: `## Contributing to Node.js API Boilerplate

We welcome contributions! Here's how you can help:

### Getting Started
1. Fork the repository
2. Create a feature branch: \`git checkout -b feature/new-feature\`
3. Make your changes
4. Run tests: \`npm test\`
5. Run linting: \`npm run lint\`
6. Commit your changes: \`git commit -m 'Add new feature'\`
7. Push to the branch: \`git push origin feature/new-feature\`
8. Open a Pull Request

### Development Guidelines
- Follow ESLint configuration
- Write comprehensive tests
- Update documentation
- Use conventional commit messages
- Ensure all tests pass`,
    goodFirstIssues: [
      {
        id: "node-issue-1",
        title: "Add rate limiting middleware",
        description: "Implement rate limiting to prevent API abuse. Should be configurable and include different limits for different endpoints.",
        labels: ["good first issue", "enhancement", "security"],
        difficulty: "Medium",
        estimatedTime: "4-6 hours",
        url: "https://github.com/example/nodejs-api-boilerplate/issues/23",
        createdAt: "2024-01-18",
        author: "maintainer3"
      },
      {
        id: "node-issue-2",
        title: "Add API documentation with Swagger",
        description: "Set up Swagger/OpenAPI documentation for all API endpoints. Should be auto-generated from route definitions.",
        labels: ["good first issue", "documentation", "enhancement"],
        difficulty: "Easy",
        estimatedTime: "3-4 hours",
        url: "https://github.com/example/nodejs-api-boilerplate/issues/31",
        createdAt: "2024-01-19",
        author: "contributor1"
      }
    ],
    maintainers: [
      {
        id: "maintainer3",
        name: "Mike Rodriguez",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150",
        role: "Backend Lead",
        githubUrl: "https://github.com/mikerodriguez",
        contributions: 203
      }
    ],
    recentCommits: [
      {
        id: "node-commit1",
        message: "feat: add comprehensive error handling middleware",
        author: "Mike Rodriguez",
        date: "2024-01-20",
        url: "https://github.com/example/nodejs-api-boilerplate/commit/ghi789"
      },
      {
        id: "node-commit2",
        message: "test: add integration tests for auth endpoints",
        author: "Mike Rodriguez",
        date: "2024-01-19",
        url: "https://github.com/example/nodejs-api-boilerplate/commit/jkl012"
      }
    ],
    readme: `# Node.js API Boilerplate

A production-ready Node.js API boilerplate with authentication, validation, and testing.

## Features

- 🔐 JWT Authentication
- ✅ Input validation with Joi
- 🛡️ Security middleware (Helmet, CORS)
- 📝 Comprehensive logging
- 🧪 Unit and integration tests
- 📊 API monitoring and health checks
- 🗄️ MongoDB integration with Mongoose
- 🔄 Environment-based configuration

## Quick Start

\`\`\`bash
npm install
cp .env.example .env
npm run dev
\`\`\`

## API Endpoints

- \`POST /api/auth/register\` - User registration
- \`POST /api/auth/login\` - User login
- \`GET /api/users/profile\` - Get user profile
- \`GET /api/health\` - Health check

## Testing

\`\`\`bash
npm test
npm run test:watch
npm run test:coverage
\`\`\`

## License

MIT License - see [LICENSE](LICENSE) for details.`
  },
  {
    id: "4",
    title: "Python Data Science Toolkit",
    description: "Essential tools and utilities for data science projects in Python",
    image: "https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    stars: 428,
    forks: 156,
    tags: ["Python", "Data Science", "Pandas"],
    language: "Python",
    difficulty: "Intermediate",
    domain: "Data Science",
    activityLevel: "High",
    lastUpdated: "2024-01-22",
    contributors: 23,
    issues: 18,
    license: "Apache-2.0",
    keywords: ["python", "data-science", "pandas", "numpy", "visualization"],
    longDescription: "A comprehensive Python toolkit for data science projects featuring essential utilities, data processing functions, visualization helpers, and machine learning utilities. Perfect for data scientists and analysts looking to streamline their workflow.",
    repositoryUrl: "https://github.com/example/python-ds-toolkit",
    demoUrl: "https://python-ds-toolkit-demo.streamlit.app",
    documentationUrl: "https://python-ds-toolkit.readthedocs.io",
    technologies: ["Python", "Pandas", "NumPy", "Matplotlib", "Seaborn", "Scikit-learn", "Jupyter", "Streamlit"],
    prerequisites: ["Python 3.8+", "Basic data science knowledge", "Pandas familiarity", "Jupyter Notebook"],
    setupInstructions: `## Getting Started

1. Clone the repository:
\`\`\`bash
git clone https://github.com/example/python-ds-toolkit.git
cd python-ds-toolkit
\`\`\`

2. Create virtual environment:
\`\`\`bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\\Scripts\\activate
\`\`\`

3. Install dependencies:
\`\`\`bash
pip install -r requirements.txt
\`\`\`

4. Install in development mode:
\`\`\`bash
pip install -e .
\`\`\`

5. Run Jupyter notebooks:
\`\`\`bash
jupyter notebook examples/
\`\`\``,
    contributionGuide: `## Contributing to Python Data Science Toolkit

We welcome contributions! Here's how you can help:

### Getting Started
1. Fork the repository
2. Create a feature branch: \`git checkout -b feature/new-utility\`
3. Make your changes
4. Run tests: \`pytest\`
5. Run linting: \`flake8 .\`
6. Commit your changes: \`git commit -m 'Add new utility'\`
7. Push to the branch: \`git push origin feature/new-utility\`
8. Open a Pull Request

### Development Guidelines
- Follow PEP 8 style guide
- Write comprehensive docstrings
- Add unit tests for new functions
- Update documentation
- Include example usage`,
    goodFirstIssues: [
      {
        id: "python-issue-1",
        title: "Add data validation utilities",
        description: "Create utility functions for common data validation tasks like checking for missing values, data types, and outliers.",
        labels: ["good first issue", "enhancement", "utilities"],
        difficulty: "Easy",
        estimatedTime: "3-4 hours",
        url: "https://github.com/example/python-ds-toolkit/issues/12",
        createdAt: "2024-01-20",
        author: "maintainer4"
      }
    ],
    maintainers: [
      {
        id: "maintainer4",
        name: "Dr. Lisa Wang",
        avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150",
        role: "Data Science Lead",
        githubUrl: "https://github.com/lisawang",
        contributions: 167
      }
    ],
    recentCommits: [
      {
        id: "python-commit1",
        message: "feat: add advanced visualization utilities",
        author: "Dr. Lisa Wang",
        date: "2024-01-22",
        url: "https://github.com/example/python-ds-toolkit/commit/mno345"
      }
    ],
    readme: `# Python Data Science Toolkit

Essential tools and utilities for data science projects in Python.

## Features

- 📊 Data visualization utilities
- 🧹 Data cleaning and preprocessing
- 📈 Statistical analysis helpers
- 🤖 Machine learning utilities
- 📝 Report generation tools
- 🔍 Data exploration functions

## Installation

\`\`\`bash
pip install python-ds-toolkit
\`\`\`

## Quick Start

\`\`\`python
from ds_toolkit import DataExplorer, Visualizer

# Load and explore data
explorer = DataExplorer(df)
explorer.summary()

# Create visualizations
viz = Visualizer(df)
viz.correlation_heatmap()
\`\`\`

## License

Apache-2.0 License - see [LICENSE](LICENSE) for details.`
  },
  {
    id: "5",
    title: "Flutter Mobile App Template",
    description: "Cross-platform mobile app template with modern UI and state management",
    image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    stars: 167,
    forks: 45,
    tags: ["Flutter", "Dart", "Mobile"],
    language: "Dart",
    difficulty: "Intermediate",
    domain: "Mobile Development",
    activityLevel: "Medium",
    lastUpdated: "2024-01-19",
    contributors: 7,
    issues: 9,
    license: "BSD-3-Clause",
    keywords: ["flutter", "mobile", "cross-platform", "dart", "ui"],
    longDescription: "A comprehensive Flutter mobile app template featuring modern UI components, state management with Bloc, navigation, authentication, and best practices for cross-platform mobile development. Perfect for kickstarting your next mobile project.",
    repositoryUrl: "https://github.com/example/flutter-app-template",
    demoUrl: "https://flutter-app-template-demo.web.app",
    documentationUrl: "https://flutter-app-template.gitbook.io",
    technologies: ["Flutter", "Dart", "Bloc", "Firebase", "Dio", "Hive", "Go Router", "Flutter Hooks"],
    prerequisites: ["Flutter SDK", "Dart knowledge", "Mobile development basics", "Android Studio or VS Code"],
    setupInstructions: `## Getting Started

1. Clone the repository:
\`\`\`bash
git clone https://github.com/example/flutter-app-template.git
cd flutter-app-template
\`\`\`

2. Install Flutter dependencies:
\`\`\`bash
flutter pub get
\`\`\`

3. Run code generation:
\`\`\`bash
flutter packages pub run build_runner build
\`\`\`

4. Run the app:
\`\`\`bash
flutter run
\`\`\`

5. For iOS (macOS only):
\`\`\`bash
cd ios && pod install && cd ..
flutter run -d ios
\`\`\``,
    contributionGuide: `## Contributing to Flutter App Template

We welcome contributions! Here's how you can help:

### Getting Started
1. Fork the repository
2. Create a feature branch: \`git checkout -b feature/new-feature\`
3. Make your changes
4. Run tests: \`flutter test\`
5. Run analysis: \`flutter analyze\`
6. Commit your changes: \`git commit -m 'Add new feature'\`
7. Push to the branch: \`git push origin feature/new-feature\`
8. Open a Pull Request

### Development Guidelines
- Follow Flutter/Dart style guide
- Write widget tests
- Update documentation
- Use Bloc for state management
- Follow Material Design principles`,
    goodFirstIssues: [
      {
        id: "flutter-issue-1",
        title: "Add biometric authentication",
        description: "Implement fingerprint and face ID authentication using local_auth package. Should integrate with existing auth flow.",
        labels: ["good first issue", "enhancement", "authentication"],
        difficulty: "Medium",
        estimatedTime: "5-7 hours",
        url: "https://github.com/example/flutter-app-template/issues/8",
        createdAt: "2024-01-17",
        author: "maintainer5"
      }
    ],
    maintainers: [
      {
        id: "maintainer5",
        name: "Carlos Silva",
        avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150",
        role: "Mobile Lead",
        githubUrl: "https://github.com/carlossilva",
        contributions: 94
      }
    ],
    recentCommits: [
      {
        id: "flutter-commit1",
        message: "feat: add dark theme support with system preference detection",
        author: "Carlos Silva",
        date: "2024-01-19",
        url: "https://github.com/example/flutter-app-template/commit/pqr678"
      }
    ],
    readme: `# Flutter Mobile App Template

A cross-platform mobile app template with modern UI and state management.

## Features

- 🎨 Modern Material Design UI
- 🔐 Authentication with Firebase
- 🗂️ State management with Bloc
- 🌐 API integration with Dio
- 💾 Local storage with Hive
- 🧭 Navigation with Go Router
- 🌙 Dark/Light theme support
- 📱 Responsive design

## Getting Started

\`\`\`bash
flutter pub get
flutter packages pub run build_runner build
flutter run
\`\`\`

## Project Structure

\`\`\`
lib/
├── core/           # Core utilities and constants
├── data/           # Data layer (repositories, models)
├── domain/         # Business logic layer
├── presentation/   # UI layer (pages, widgets, blocs)
└── main.dart       # App entry point
\`\`\`

## License

BSD-3-Clause License - see [LICENSE](LICENSE) for details.`
  }
];

// Export individual projects for easy access
export const REACT_DASHBOARD = PROJECTS_DATA[0];
export const VUE_COMPONENTS = PROJECTS_DATA[1];
export const NODE_API = PROJECTS_DATA[2];
export const PYTHON_TOOLKIT = PROJECTS_DATA[3];
export const FLUTTER_TEMPLATE = PROJECTS_DATA[4];

// Helper functions
export const getProjectById = (id: string): Project | undefined => {
  return PROJECTS_DATA.find(project => project.id === id);
};

export const getProjectsByLanguage = (language: string): Project[] => {
  return PROJECTS_DATA.filter(project =>
    project.language.toLowerCase() === language.toLowerCase()
  );
};

export const getProjectsByDifficulty = (difficulty: string): Project[] => {
  return PROJECTS_DATA.filter(project =>
    project.difficulty.toLowerCase() === difficulty.toLowerCase()
  );
};

export const getProjectsByDomain = (domain: string): Project[] => {
  return PROJECTS_DATA.filter(project =>
    project.domain.toLowerCase() === domain.toLowerCase()
  );
};
