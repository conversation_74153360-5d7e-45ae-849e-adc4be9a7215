// Quick token verification utility
export function verifyGitHubToken() {
  console.log('🔑 GitHub Token Verification');
  console.log('============================');
  
  const token = import.meta.env.VITE_GITHUB_TOKEN;
  
  if (!token) {
    console.log('❌ No token found');
    console.log('📝 Check:');
    console.log('   1. File exists: .env.local');
    console.log('   2. Format: VITE_GITHUB_TOKEN=your_token');
    console.log('   3. Restart server: npm run dev');
    return false;
  }
  
  console.log('✅ Token found');
  console.log('📊 Token info:');
  console.log(`   Length: ${token.length} characters`);
  console.log(`   Prefix: ${token.substring(0, 4)}...`);
  console.log(`   Type: ${token.startsWith('ghp_') ? 'Personal Access Token' : 'Unknown format'}`);
  
  return true;
}

export async function testTokenAPI() {
  console.log('🧪 Testing GitHub API with token...');
  
  const token = import.meta.env.VITE_GITHUB_TOKEN;
  if (!token) {
    console.log('❌ No token to test');
    return;
  }
  
  try {
    const response = await fetch('https://api.github.com/rate_limit', {
      headers: {
        'Authorization': `token ${token}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'IDXCodeHub/1.0.0',
      },
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Token works!');
      console.log('📊 Rate limit with token:');
      console.log(`   Limit: ${data.resources.core.limit}/hour`);
      console.log(`   Used: ${data.resources.core.used}`);
      console.log(`   Remaining: ${data.resources.core.remaining}`);
    } else {
      console.log('❌ Token failed');
      console.log(`   Status: ${response.status}`);
      console.log(`   Message: ${response.statusText}`);
      
      if (response.status === 401) {
        console.log('🔧 Token is invalid or expired');
      }
    }
  } catch (error) {
    console.log('❌ API test failed:', error);
  }
}

// Make available in console
if (typeof window !== 'undefined') {
  (window as any).tokenTest = {
    verifyGitHubToken,
    testTokenAPI,
  };
  
  console.log('🔑 Token test available: window.tokenTest.verifyGitHubToken()');
}
