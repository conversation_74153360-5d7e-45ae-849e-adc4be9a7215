import React from "react";
import { Filter, Search, Clock, TrendingUp } from "lucide-react";
import { Project } from "../../../types/project.types";

interface SearchResultsSummaryProps {
  totalProjects: number;
  filteredProjects: Project[];
  searchQuery: string;
  activeFiltersCount: number;
  onClearFilters: () => void;
}

export function SearchResultsSummary({
  totalProjects,
  filteredProjects,
  searchQuery,
  activeFiltersCount,
  onClearFilters
}: SearchResultsSummaryProps) {
  const hasFilters = activeFiltersCount > 0 || searchQuery.trim().length > 0;
  const filteredCount = filteredProjects.length;
  const percentage = totalProjects > 0 ? Math.round((filteredCount / totalProjects) * 100) : 0;

  // Quick stats about filtered results
  const stats = {
    beginnerFriendly: filteredProjects.filter(p => p.difficulty === 'Beginner').length,
    highActivity: filteredProjects.filter(p => p.activityLevel === 'High').length,
    withIssues: filteredProjects.filter(p => p.issues > 0).length,
    recentlyUpdated: filteredProjects.filter(p => {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      return new Date(p.lastUpdated) > thirtyDaysAgo;
    }).length,
  };

  const getResultsText = () => {
    if (!hasFilters) {
      return `Showing all ${totalProjects} projects`;
    }

    if (filteredCount === 0) {
      return "No projects match your criteria";
    }

    if (filteredCount === totalProjects) {
      return `All ${totalProjects} projects match your criteria`;
    }

    return `Found ${filteredCount} of ${totalProjects} projects (${percentage}%)`;
  };

  const getSearchSummary = () => {
    const parts = [];

    if (searchQuery.trim()) {
      parts.push(`searching for "${searchQuery}"`);
    }

    if (activeFiltersCount > 0) {
      parts.push(`${activeFiltersCount} filter${activeFiltersCount > 1 ? 's' : ''} applied`);
    }

    return parts.length > 0 ? parts.join(' with ') : '';
  };

  return (
    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-blue-400" />
            <span className="text-lg font-medium text-gray-200">
              {getResultsText()}
            </span>
          </div>

          {hasFilters && (
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <Filter className="h-3 w-3" />
              <span>{getSearchSummary()}</span>
              {activeFiltersCount > 0 && (
                <button
                  onClick={onClearFilters}
                  className="text-blue-400 hover:text-blue-300 underline ml-2"
                >
                  Clear filters
                </button>
              )}
            </div>
          )}
        </div>

        {/* Quick Stats */}
        {filteredCount > 0 && (
          <div className="flex items-center space-x-4 text-sm">
            {stats.beginnerFriendly > 0 && (
              <div className="flex items-center space-x-1 text-green-400">
                <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                <span>{stats.beginnerFriendly} beginner-friendly</span>
              </div>
            )}

            {stats.highActivity > 0 && (
              <div className="flex items-center space-x-1 text-blue-400">
                <TrendingUp className="h-3 w-3" />
                <span>{stats.highActivity} highly active</span>
              </div>
            )}

            {stats.withIssues > 0 && (
              <div className="flex items-center space-x-1 text-orange-400">
                <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                <span>{stats.withIssues} need help</span>
              </div>
            )}

            {stats.recentlyUpdated > 0 && (
              <div className="flex items-center space-x-1 text-purple-400">
                <Clock className="h-3 w-3" />
                <span>{stats.recentlyUpdated} recently updated</span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* No results help text */}
      {filteredCount === 0 && hasFilters && (
        <div className="mt-4 p-4 bg-gray-700/50 rounded-lg border border-gray-600/50">
          <h4 className="text-sm font-medium text-gray-300 mb-2">Try adjusting your search:</h4>
          <ul className="text-sm text-gray-400 space-y-1">
            <li>• Remove some filters to see more results</li>
            <li>• Try different keywords or technologies</li>
            <li>• Check for typos in your search query</li>
            <li>• Browse by difficulty level or domain instead</li>
          </ul>
        </div>
      )}

      {/* Results insights */}
      {filteredCount > 0 && filteredCount < totalProjects && (
        <div className="mt-3 pt-3 border-t border-gray-700/50">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Results filtered from {totalProjects} total projects</span>
            <div className="flex items-center space-x-2">
              <div className="w-20 bg-gray-700 rounded-full h-1">
                <div
                  className="h-1 bg-blue-500 rounded-full transition-all duration-300"
                  style={{ width: `${percentage}%` }}
                />
              </div>
              <span>{percentage}%</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
