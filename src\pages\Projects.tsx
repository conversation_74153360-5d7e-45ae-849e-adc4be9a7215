import React, { useState, useEffect, useCallback } from "react";
import { ProjectCard } from "@/components/features/projects/projectCard";
import { ProjectSearch } from "@/components/features/search/ProjectSearch";
import { SearchStats } from "@/components/features/search/SearchStats";
import { SearchResultsSummary } from "@/components/features/search/SearchResultsSummary";
import { CenteredLoadingSpinner, CenteredErrorMessage } from "@/components/ui";
import { GitHubRateLimitWarning } from "@/components/ui/GitHubRateLimitWarning";
import { useProjects } from "@/hooks/useProjects";
import { Project, ProjectFilters } from "@/types/project.types";
import { Grid, List, BarChart3, TrendingUp, RefreshCw } from "lucide-react";

export function Projects() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showStats, setShowStats] = useState(false);
  const [filters, setFilters] = useState<ProjectFilters>({});

  // Use the new projects hook
  const {
    projects,
    loading,
    error,
    totalCount,
    searchProjects,
    refreshProjects,
    clearError
  } = useProjects(filters);

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: ProjectFilters) => {
    setFilters(newFilters);
    searchProjects(newFilters);
  }, [searchProjects]);

  const handleRefresh = useCallback(() => {
    refreshProjects();
  }, [refreshProjects]);

  // Show loading state
  if (loading && projects.length === 0) {
    return <CenteredLoadingSpinner text="Discovering amazing open source projects..." />;
  }

  // Show error state
  if (error && projects.length === 0) {
    return (
      <CenteredErrorMessage
        title="Failed to load projects"
        message={error}
        onRetry={handleRefresh}
        retryText="Try Again"
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* GitHub Rate Limit Warning */}
      <GitHubRateLimitWarning />

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">Explore Projects</h1>
          <p className="text-gray-400 mt-1">
            Discover open-source projects • {projects.length} results
            {totalCount > projects.length && ` of ${totalCount} total`}
          </p>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center space-x-4">
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors bg-gray-800 text-gray-300 hover:bg-gray-700 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            <span className="text-sm">Refresh</span>
          </button>

          <button
            onClick={() => setShowStats(!showStats)}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
              showStats
                ? 'bg-purple-600 text-white'
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
            }`}
          >
            <TrendingUp className="h-4 w-4" />
            <span className="text-sm">Stats</span>
          </button>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">View:</span>
            <div className="flex bg-gray-800 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-gray-300'
                }`}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'list'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-gray-300'
                }`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Project Search Component */}
      <ProjectSearch
        onFiltersChange={handleFiltersChange}
        loading={loading}
        projects={projects}
      />

      {/* Search Results Summary */}
      <SearchResultsSummary
        totalProjects={totalCount}
        filteredProjects={projects}
        searchQuery={filters.query || ''}
        activeFiltersCount={Object.keys(filters).length}
        onClearFilters={() => handleFiltersChange({})}
      />

      {/* Search Statistics */}
      {showStats && (
        <SearchStats
          projects={projects}
          filteredProjects={projects}
        />
      )}

      {/* Error message for partial failures */}
      {error && projects.length > 0 && (
        <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
          <p className="text-yellow-200 text-sm">
            Some projects couldn't be loaded: {error}
          </p>
        </div>
      )}

      {/* Results Summary */}
      {projects.length === 0 && !loading ? (
        <div className="text-center py-12">
          <BarChart3 className="h-12 w-12 text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">No projects found</h3>
          <p className="text-gray-500">Try adjusting your search criteria or filters</p>
        </div>
      ) : (
        <>
          {/* Projects Grid/List */}
          <div className={
            viewMode === 'grid'
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              : "space-y-4"
          }>
            {projects.map((project) => (
              <ProjectCard key={project.id} {...project} />
            ))}
          </div>

          {/* Loading more indicator */}
          {loading && projects.length > 0 && (
            <div className="text-center py-4">
              <CenteredLoadingSpinner size="md" text="Loading more projects..." />
            </div>
          )}

          {/* Load More Button (for future pagination) */}
          {projects.length >= 20 && !loading && (
            <div className="text-center pt-8">
              <button
                className="bg-gray-800 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                onClick={handleRefresh}
              >
                Load More Projects
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
