import React, { useState } from "react";
import { ProjectCard } from "../components/features/projects/projectCard";
import { AdvancedSearch } from "../components/features/search/AdvancedSearch";
import { SearchStats } from "../components/features/search/SearchStats";
import { SearchResultsSummary } from "../components/features/search/SearchResultsSummary";
import { PROJECTS_DATA } from "../constants/projects.data";
import { Project } from "../types/project.types";
import { Grid, List, BarChart3, TrendingUp } from "lucide-react";

export function Projects() {
  const [filteredProjects, setFilteredProjects] = useState<Project[]>(PROJECTS_DATA);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState("stars");
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>("desc");
  const [showStats, setShowStats] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  const handleFilterChange = (filtered: Project[]) => {
    setFilteredProjects(filtered);
  };

  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
  };

  const handleSearchChange = (query: string, filtersCount: number) => {
    setSearchQuery(query);
    setActiveFiltersCount(filtersCount);
  };

  const clearAllFilters = () => {
    setSearchQuery("");
    setActiveFiltersCount(0);
    setFilteredProjects(PROJECTS_DATA);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">Explore Projects</h1>
          <p className="text-gray-400 mt-1">
            Discover {PROJECTS_DATA.length} open-source projects • {filteredProjects.length} results
          </p>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowStats(!showStats)}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
              showStats
                ? 'bg-purple-600 text-white'
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
            }`}
          >
            <TrendingUp className="h-4 w-4" />
            <span className="text-sm">Stats</span>
          </button>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">View:</span>
            <div className="flex bg-gray-800 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-gray-300'
                }`}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'list'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-gray-300'
                }`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Search Component */}
      <AdvancedSearch
        projects={PROJECTS_DATA}
        onFilterChange={handleFilterChange}
        onSortChange={handleSortChange}
        onSearchChange={handleSearchChange}
      />

      {/* Search Results Summary */}
      <SearchResultsSummary
        totalProjects={PROJECTS_DATA.length}
        filteredProjects={filteredProjects}
        searchQuery={searchQuery}
        activeFiltersCount={activeFiltersCount}
        onClearFilters={clearAllFilters}
      />

      {/* Search Statistics */}
      {showStats && (
        <SearchStats
          projects={PROJECTS_DATA}
          filteredProjects={filteredProjects}
        />
      )}

      {/* Results Summary */}
      {filteredProjects.length === 0 ? (
        <div className="text-center py-12">
          <BarChart3 className="h-12 w-12 text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">No projects found</h3>
          <p className="text-gray-500">Try adjusting your search criteria or filters</p>
        </div>
      ) : (
        <>
          {/* Projects Grid/List */}
          <div className={
            viewMode === 'grid'
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              : "space-y-4"
          }>
            {filteredProjects.map((project) => (
              <ProjectCard key={project.id} {...project} />
            ))}
          </div>

          {/* Load More Button (for future pagination) */}
          {filteredProjects.length >= 20 && (
            <div className="text-center pt-8">
              <button className="bg-gray-800 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                Load More Projects
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
