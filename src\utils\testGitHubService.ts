// Utility function to test GitHub service functionality
import { githubService } from '@/services/githubService';
import { ProjectFilters } from '@/types/project.types';

export async function testGitHubService() {
  console.log('🧪 Testing GitHub Service...');
  
  try {
    // Test basic search
    console.log('📡 Testing basic search...');
    const searchResult = await githubService.searchRepositories({
      query: 'react',
      minStars: 100,
      languages: ['JavaScript', 'TypeScript'],
    });
    
    console.log(`✅ Found ${searchResult.projects.length} projects`);
    console.log('Sample project:', searchResult.projects[0]?.title);
    
    // Test trending repositories
    console.log('📈 Testing trending repositories...');
    const trendingProjects = await githubService.getTrendingRepositories('JavaScript', 'weekly');
    
    console.log(`✅ Found ${trendingProjects.length} trending projects`);
    console.log('Sample trending project:', trendingProjects[0]?.title);
    
    return {
      success: true,
      searchResults: searchResult.projects.length,
      trendingResults: trendingProjects.length,
    };
  } catch (error) {
    console.error('❌ GitHub Service test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Function to test with different filters
export async function testProjectFilters() {
  console.log('🔍 Testing project filters...');
  
  const testCases: { name: string; filters: ProjectFilters }[] = [
    {
      name: 'Beginner-friendly React projects',
      filters: {
        query: 'react',
        languages: ['JavaScript', 'TypeScript'],
        difficulties: ['Beginner'],
        hasGoodFirstIssues: true,
        minStars: 50,
      },
    },
    {
      name: 'Python machine learning projects',
      filters: {
        query: 'machine learning',
        languages: ['Python'],
        minStars: 100,
      },
    },
    {
      name: 'Popular web development projects',
      filters: {
        domains: ['Web Development'],
        minStars: 500,
        activityLevels: ['High'],
      },
    },
  ];
  
  const results = [];
  
  for (const testCase of testCases) {
    try {
      console.log(`🧪 Testing: ${testCase.name}`);
      const result = await githubService.searchRepositories(testCase.filters);
      console.log(`✅ Found ${result.projects.length} projects for "${testCase.name}"`);
      
      results.push({
        name: testCase.name,
        count: result.projects.length,
        success: true,
      });
    } catch (error) {
      console.error(`❌ Failed test: ${testCase.name}`, error);
      results.push({
        name: testCase.name,
        count: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
  
  return results;
}

// Function to check API rate limits
export async function checkGitHubRateLimit() {
  console.log('⏱️ Checking GitHub API rate limit...');
  
  try {
    const response = await fetch('https://api.github.com/rate_limit', {
      headers: {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'IDXCodeHub/1.0.0',
        ...(import.meta.env.VITE_GITHUB_TOKEN && {
          'Authorization': `token ${import.meta.env.VITE_GITHUB_TOKEN}`,
        }),
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    const { core } = data.resources;
    
    console.log(`✅ Rate limit status:`);
    console.log(`   Limit: ${core.limit} requests/hour`);
    console.log(`   Used: ${core.used} requests`);
    console.log(`   Remaining: ${core.remaining} requests`);
    console.log(`   Reset: ${new Date(core.reset * 1000).toLocaleTimeString()}`);
    
    return {
      limit: core.limit,
      used: core.used,
      remaining: core.remaining,
      resetTime: new Date(core.reset * 1000),
      hasToken: !!import.meta.env.VITE_GITHUB_TOKEN,
    };
  } catch (error) {
    console.error('❌ Failed to check rate limit:', error);
    return {
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Development helper to run all tests
export async function runAllTests() {
  console.log('🚀 Running all GitHub service tests...\n');
  
  // Check rate limit first
  const rateLimitResult = await checkGitHubRateLimit();
  console.log('\n');
  
  // Test basic functionality
  const basicTest = await testGitHubService();
  console.log('\n');
  
  // Test different filters
  const filterTests = await testProjectFilters();
  console.log('\n');
  
  console.log('📊 Test Summary:');
  console.log('================');
  console.log(`Rate Limit Check: ${rateLimitResult.error ? '❌' : '✅'}`);
  console.log(`Basic Service Test: ${basicTest.success ? '✅' : '❌'}`);
  console.log(`Filter Tests: ${filterTests.filter(t => t.success).length}/${filterTests.length} passed`);
  
  return {
    rateLimit: rateLimitResult,
    basicTest,
    filterTests,
  };
}

// Export for console testing
if (typeof window !== 'undefined') {
  (window as any).testGitHub = {
    runAllTests,
    testGitHubService,
    testProjectFilters,
    checkGitHubRateLimit,
  };
}
