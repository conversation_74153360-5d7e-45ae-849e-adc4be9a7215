# 🛠️ Development Guide

This guide covers development practices, coding standards, and workflows for IDXCodeHub.

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Git
- VS Code (recommended)

### Initial Setup
```bash
# Clone the repository
git clone https://github.com/your-username/idxcodehub.git
cd idxcodehub

# Install dependencies
npm install

# Start development server
npm run dev
```

### Recommended VS Code Extensions
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Tailwind CSS IntelliSense
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens

## 📝 Coding Standards

### TypeScript Guidelines

#### Type Definitions
```typescript
// ✅ Good - Explicit interface definitions
interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

// ✅ Good - Generic types
interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

// ❌ Avoid - Using 'any'
const userData: any = fetchUser();
```

#### Component Props
```typescript
// ✅ Good - Explicit prop interfaces
interface ButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  children: React.ReactNode;
}

export function Button({ variant = 'primary', size = 'md', onClick, children }: ButtonProps) {
  // Component implementation
}
```

### React Best Practices

#### Component Structure
```typescript
// ✅ Good - Consistent component structure
import React, { useState, useEffect } from 'react';
import { SomeIcon } from 'lucide-react';
import { Button } from 'src/components/ui';
import { useAuth } from 'src/contexts/AuthContext';

interface ComponentProps {
  // Props interface
}

export function ComponentName({ prop1, prop2 }: ComponentProps) {
  // Hooks
  const [state, setState] = useState();
  const { user } = useAuth();

  // Effects
  useEffect(() => {
    // Effect logic
  }, []);

  // Event handlers
  const handleClick = () => {
    // Handler logic
  };

  // Render helpers
  const renderContent = () => {
    // Helper logic
  };

  // Main render
  return (
    <div className="component-container">
      {/* JSX content */}
    </div>
  );
}
```

#### Custom Hooks
```typescript
// ✅ Good - Custom hook pattern
export function useSearch() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const search = useCallback(async (searchQuery: string) => {
    setLoading(true);
    try {
      const data = await searchAPI(searchQuery);
      setResults(data);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  return { query, setQuery, results, loading, search };
}
```

### CSS/Styling Guidelines

#### Tailwind CSS Usage
```typescript
// ✅ Good - Semantic class grouping
<div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg border border-gray-700">
  <h2 className="text-lg font-semibold text-gray-200">Title</h2>
  <Button variant="primary" size="sm">Action</Button>
</div>

// ✅ Good - Responsive design
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Grid items */}
</div>

// ❌ Avoid - Overly long class strings
<div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg border border-gray-700 hover:bg-gray-700 transition-colors duration-200 shadow-lg hover:shadow-xl">
```

#### Component Styling
```typescript
// ✅ Good - Conditional classes with utility
const buttonClasses = [
  'px-4 py-2 rounded-lg font-medium transition-colors',
  variant === 'primary' ? 'bg-blue-600 text-white' : 'bg-gray-600 text-gray-200',
  size === 'sm' ? 'text-sm' : 'text-base',
  disabled && 'opacity-50 cursor-not-allowed'
].filter(Boolean).join(' ');
```

## 🏗️ Architecture Patterns

### Service Layer Pattern
```typescript
// services/projectService.ts
class ProjectService {
  async getProjects(filters?: SearchFilters): Promise<Project[]> {
    // Implementation
  }

  async getProjectById(id: string): Promise<Project | null> {
    // Implementation
  }

  async bookmarkProject(userId: string, projectId: string): Promise<void> {
    // Implementation
  }
}

export const projectService = new ProjectService();
```

### Context Pattern
```typescript
// contexts/AuthContext.tsx
interface AuthContextType {
  user: User | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
}
```

### Custom Hook Pattern
```typescript
// hooks/useLocalStorage.ts
export function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  };

  return [storedValue, setValue] as const;
}
```

## 🧪 Testing Guidelines

### Component Testing
```typescript
// __tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from 'src/components/ui/Button';

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Hook Testing
```typescript
// __tests__/useSearch.test.ts
import { renderHook, act } from '@testing-library/react';
import { useSearch } from 'src/hooks/useSearch';

describe('useSearch', () => {
  it('should update query when setQuery is called', () => {
    const { result } = renderHook(() => useSearch());

    act(() => {
      result.current.updateFilters({ query: 'react' });
    });

    expect(result.current.filters.query).toBe('react');
  });
});
```

## 📦 Build and Deployment

### Build Commands
```bash
# Development
npm run dev

# Production build
npm run build

# Preview production build
npm run preview

# Type checking
npm run type-check

# Linting
npm run lint
npm run lint:fix
```

### Environment Variables
```env
# .env.local
VITE_GITHUB_CLIENT_ID=your_github_client_id
VITE_GITHUB_REDIRECT_URI=http://localhost:5173/auth/callback
VITE_API_BASE_URL=http://localhost:3001/api
VITE_ENABLE_ANALYTICS=false
```

## 🔄 Git Workflow

### Branch Naming
- `feature/feature-name` - New features
- `bugfix/bug-description` - Bug fixes
- `hotfix/critical-fix` - Critical fixes
- `refactor/refactor-description` - Code refactoring

### Commit Messages
Follow conventional commits:
```bash
feat: add user authentication system
fix: resolve search filter bug
docs: update API documentation
style: improve button component styling
refactor: reorganize folder structure
test: add unit tests for search hook
```

### Pull Request Process
1. Create feature branch from `main`
2. Make changes with proper commits
3. Update tests and documentation
4. Create pull request with description
5. Request code review
6. Address feedback
7. Merge after approval

## 🐛 Debugging

### Common Issues

#### Import Errors
```typescript
// ❌ Wrong - Relative imports for distant files
import { Button } from '../../../components/ui/Button';

// ✅ Correct - Absolute imports
import { Button } from 'src/components/ui';
```

#### TypeScript Errors
```typescript
// ❌ Wrong - Missing type definitions
const user = getUser(); // Type 'unknown'

// ✅ Correct - Proper typing
const user: User = getUser();
// or
const user = getUser() as User;
```

### Development Tools
- React Developer Tools
- Redux DevTools (if using Redux)
- TypeScript Error Lens
- Console debugging with proper logging

## 📚 Resources

- [React Documentation](https://reactjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Testing Library Documentation](https://testing-library.com/docs)
- [Vite Documentation](https://vitejs.dev/guide)
