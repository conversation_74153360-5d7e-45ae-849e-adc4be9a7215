import React from "react";
import { TrendingUp, Code, Users, Star } from "lucide-react";
import { Project } from "../../../types/project.types";

interface SearchStatsProps {
  projects: Project[];
  filteredProjects: Project[];
}

export function SearchStats({ projects, filteredProjects }: SearchStatsProps) {
  const stats = {
    totalProjects: projects.length,
    filteredProjects: filteredProjects.length,
    languages: new Set(filteredProjects.map(p => p.language)).size,
    domains: new Set(filteredProjects.map(p => p.domain)).size,
    totalStars: filteredProjects.reduce((sum, p) => sum + p.stars, 0),
    totalContributors: filteredProjects.reduce((sum, p) => sum + p.contributors, 0),
    avgStars: Math.round(filteredProjects.reduce((sum, p) => sum + p.stars, 0) / filteredProjects.length) || 0,
    beginnerFriendly: filteredProjects.filter(p => p.difficulty === 'Beginner').length,
    highActivity: filteredProjects.filter(p => p.activityLevel === 'High').length,
  };

  const difficultyDistribution = {
    Beginner: filteredProjects.filter(p => p.difficulty === 'Beginner').length,
    Intermediate: filteredProjects.filter(p => p.difficulty === 'Intermediate').length,
    Advanced: filteredProjects.filter(p => p.difficulty === 'Advanced').length,
  };

  const topLanguages = Object.entries(
    filteredProjects.reduce((acc, project) => {
      acc[project.language] = (acc[project.language] || 0) + 1;
      return acc;
    }, {} as Record<string, number>)
  )
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5);

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      <h3 className="text-lg font-semibold mb-4 flex items-center">
        <TrendingUp className="h-5 w-5 mr-2 text-blue-500" />
        Search Statistics
      </h3>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-400">{stats.filteredProjects}</div>
          <div className="text-sm text-gray-400">Projects Found</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-400">{stats.languages}</div>
          <div className="text-sm text-gray-400">Languages</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-yellow-400">{stats.avgStars}</div>
          <div className="text-sm text-gray-400">Avg Stars</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-400">{stats.domains}</div>
          <div className="text-sm text-gray-400">Domains</div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Difficulty Distribution */}
        <div>
          <h4 className="text-sm font-medium text-gray-300 mb-3">Difficulty Distribution</h4>
          <div className="space-y-2">
            {Object.entries(difficultyDistribution).map(([difficulty, count]) => {
              const percentage = stats.filteredProjects > 0 ? (count / stats.filteredProjects) * 100 : 0;
              const colorClass =
                difficulty === 'Beginner' ? 'bg-green-500' :
                difficulty === 'Intermediate' ? 'bg-yellow-500' : 'bg-red-500';

              return (
                <div key={difficulty} className="flex items-center justify-between">
                  <span className="text-sm text-gray-300">{difficulty}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${colorClass}`}
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-xs text-gray-400 w-8">{count}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Top Languages */}
        <div>
          <h4 className="text-sm font-medium text-gray-300 mb-3">Top Languages</h4>
          <div className="space-y-2">
            {topLanguages.map(([language, count]) => {
              const percentage = stats.filteredProjects > 0 ? (count / stats.filteredProjects) * 100 : 0;

              return (
                <div key={language} className="flex items-center justify-between">
                  <span className="text-sm text-gray-300">{language}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-700 rounded-full h-2">
                      <div
                        className="h-2 rounded-full bg-blue-500"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-xs text-gray-400 w-8">{count}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="mt-6 pt-6 border-t border-gray-700">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div className="flex flex-col items-center">
            <Star className="h-5 w-5 text-yellow-400 mb-1" />
            <span className="text-lg font-semibold text-gray-300">{stats.totalStars.toLocaleString()}</span>
            <span className="text-xs text-gray-500">Total Stars</span>
          </div>
          <div className="flex flex-col items-center">
            <Users className="h-5 w-5 text-blue-400 mb-1" />
            <span className="text-lg font-semibold text-gray-300">{stats.totalContributors}</span>
            <span className="text-xs text-gray-500">Contributors</span>
          </div>
          <div className="flex flex-col items-center">
            <Code className="h-5 w-5 text-green-400 mb-1" />
            <span className="text-lg font-semibold text-gray-300">{stats.beginnerFriendly}</span>
            <span className="text-xs text-gray-500">Beginner Friendly</span>
          </div>
          <div className="flex flex-col items-center">
            <TrendingUp className="h-5 w-5 text-purple-400 mb-1" />
            <span className="text-lg font-semibold text-gray-300">{stats.highActivity}</span>
            <span className="text-xs text-gray-500">High Activity</span>
          </div>
        </div>
      </div>
    </div>
  );
}
