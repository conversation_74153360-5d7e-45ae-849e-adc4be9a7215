import React, { useState, useRef, useEffect } from "react";
import {
  Search,
  Filter,
  X,
  ChevronDown,
  SlidersHorizontal,
  Calendar,
  Users,
  Star,
  GitBranch
} from "lucide-react";
import { Project } from "../../../types/project.types";

interface AdvancedSearchProps {
  projects: Project[];
  onFilterChange: (filteredProjects: Project[]) => void;
  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  onSearchChange?: (query: string, filtersCount: number) => void;
}

interface Filters {
  searchQuery: string;
  languages: string[];
  difficulties: string[];
  domains: string[];
  activityLevels: string[];
  tags: string[];
  minStars: number;
  maxStars: number;
  hasIssues: boolean;
  recentlyUpdated: boolean;
}

export function AdvancedSearch({ projects, onFilterChange, onSortChange, onSearchChange }: AdvancedSearchProps) {
  const [filters, setFilters] = useState<Filters>({
    searchQuery: "",
    languages: [],
    difficulties: [],
    domains: [],
    activityLevels: [],
    tags: [],
    minStars: 0,
    maxStars: 1000,
    hasIssues: false,
    recentlyUpdated: false,
  });

  const [showFilters, setShowFilters] = useState(false);
  const [showAutocomplete, setShowAutocomplete] = useState(false);
  const [autocompleteResults, setAutocompleteResults] = useState<string[]>([]);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const [sortBy, setSortBy] = useState("stars");
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>("desc");
  const [isFiltering, setIsFiltering] = useState(false);

  const searchInputRef = useRef<HTMLInputElement>(null);
  const autocompleteRef = useRef<HTMLDivElement>(null);

  // Extract unique values for filter options
  const languages = [...new Set(projects.map(p => p.language))].sort();
  const difficulties = [...new Set(projects.map(p => p.difficulty))].sort();
  const domains = [...new Set(projects.map(p => p.domain))].sort();
  const activityLevels = [...new Set(projects.map(p => p.activityLevel))].sort();
  const allTags = [...new Set(projects.flatMap(p => p.tags))].sort();

  // Generate autocomplete suggestions
  const generateAutocomplete = (query: string) => {
    if (!query.trim()) return [];

    const suggestions = new Set<string>();
    const lowerQuery = query.toLowerCase();

    projects.forEach(project => {
      // Add matching titles
      if (project.title.toLowerCase().includes(lowerQuery)) {
        suggestions.add(project.title);
      }

      // Add matching keywords
      project.keywords.forEach(keyword => {
        if (keyword.toLowerCase().includes(lowerQuery)) {
          suggestions.add(keyword);
        }
      });

      // Add matching tags
      project.tags.forEach(tag => {
        if (tag.toLowerCase().includes(lowerQuery)) {
          suggestions.add(tag);
        }
      });

      // Add matching languages
      if (project.language.toLowerCase().includes(lowerQuery)) {
        suggestions.add(project.language);
      }

      // Add matching domains
      if (project.domain.toLowerCase().includes(lowerQuery)) {
        suggestions.add(project.domain);
      }
    });

    return Array.from(suggestions).slice(0, 8);
  };

  // Filter projects based on current filters
  const filterProjects = () => {
    setIsFiltering(true);

    // Add a small delay to show loading state for better UX
    setTimeout(() => {
      let filtered = projects.filter(project => {
      // Search query filter
      const searchMatch = !filters.searchQuery ||
        project.title.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
        project.description.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
        project.keywords.some(keyword => keyword.toLowerCase().includes(filters.searchQuery.toLowerCase())) ||
        project.tags.some(tag => tag.toLowerCase().includes(filters.searchQuery.toLowerCase()));

      // Language filter
      const languageMatch = filters.languages.length === 0 || filters.languages.includes(project.language);

      // Difficulty filter
      const difficultyMatch = filters.difficulties.length === 0 || filters.difficulties.includes(project.difficulty);

      // Domain filter
      const domainMatch = filters.domains.length === 0 || filters.domains.includes(project.domain);

      // Activity level filter
      const activityMatch = filters.activityLevels.length === 0 || filters.activityLevels.includes(project.activityLevel);

      // Tags filter
      const tagsMatch = filters.tags.length === 0 || filters.tags.some(tag => project.tags.includes(tag));

      // Stars filter
      const starsMatch = project.stars >= filters.minStars && project.stars <= filters.maxStars;

      // Issues filter
      const issuesMatch = !filters.hasIssues || project.issues > 0;

      // Recently updated filter (within last 30 days)
      const recentMatch = !filters.recentlyUpdated ||
        new Date(project.lastUpdated) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        return searchMatch && languageMatch && difficultyMatch && domainMatch &&
               activityMatch && tagsMatch && starsMatch && issuesMatch && recentMatch;
      });

      // Sort filtered results
      filtered.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (sortBy) {
          case 'title':
            aValue = a.title.toLowerCase();
            bValue = b.title.toLowerCase();
            break;
          case 'stars':
            aValue = a.stars;
            bValue = b.stars;
            break;
          case 'forks':
            aValue = a.forks;
            bValue = b.forks;
            break;
          case 'updated':
            aValue = new Date(a.lastUpdated);
            bValue = new Date(b.lastUpdated);
            break;
          case 'contributors':
            aValue = a.contributors;
            bValue = b.contributors;
            break;
          default:
            aValue = a.stars;
            bValue = b.stars;
        }

        if (sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });

      onFilterChange(filtered);
      setIsFiltering(false);
    }, 100);
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setFilters(prev => ({ ...prev, searchQuery: value }));
    setSelectedSuggestionIndex(-1);

    if (value.trim()) {
      const suggestions = generateAutocomplete(value);
      setAutocompleteResults(suggestions);
      setShowAutocomplete(true);
    } else {
      setShowAutocomplete(false);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showAutocomplete || autocompleteResults.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev =>
          prev < autocompleteResults.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev =>
          prev > 0 ? prev - 1 : autocompleteResults.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0) {
          handleAutocompleteSelect(autocompleteResults[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowAutocomplete(false);
        setSelectedSuggestionIndex(-1);
        break;
    }
  };

  // Handle autocomplete selection
  const handleAutocompleteSelect = (suggestion: string) => {
    setFilters(prev => ({ ...prev, searchQuery: suggestion }));
    setShowAutocomplete(false);
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Handle filter toggle
  const toggleFilter = (filterType: keyof Filters, value: string) => {
    setFilters(prev => {
      const currentValues = prev[filterType] as string[];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];

      return { ...prev, [filterType]: newValues };
    });
  };

  // Handle sort change
  const handleSortChange = (newSortBy: string) => {
    const newSortOrder = sortBy === newSortBy && sortOrder === 'desc' ? 'asc' : 'desc';
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    onSortChange(newSortBy, newSortOrder);
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      searchQuery: "",
      languages: [],
      difficulties: [],
      domains: [],
      activityLevels: [],
      tags: [],
      minStars: 0,
      maxStars: 1000,
      hasIssues: false,
      recentlyUpdated: false,
    });
  };

  // Effect to filter projects when filters change
  useEffect(() => {
    filterProjects();
    // Notify parent of search changes
    if (onSearchChange) {
      onSearchChange(filters.searchQuery, activeFiltersCount);
    }
  }, [filters, sortBy, sortOrder]);

  // Close autocomplete when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (autocompleteRef.current && !autocompleteRef.current.contains(event.target as Node)) {
        setShowAutocomplete(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const activeFiltersCount =
    filters.languages.length +
    filters.difficulties.length +
    filters.domains.length +
    filters.activityLevels.length +
    filters.tags.length +
    (filters.hasIssues ? 1 : 0) +
    (filters.recentlyUpdated ? 1 : 0) +
    (filters.minStars > 0 || filters.maxStars < 1000 ? 1 : 0);

  return (
    <div className="space-y-4">
      {/* Search Bar with Autocomplete */}
      <div className="relative" ref={autocompleteRef}>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search projects, technologies, keywords..."
            value={filters.searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={() => {
              if (filters.searchQuery.trim()) {
                setShowAutocomplete(true);
              }
            }}
            className="w-full bg-gray-800 rounded-lg pl-10 pr-4 py-3 text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 border border-gray-700"
          />
        </div>

        {/* Autocomplete Dropdown */}
        {showAutocomplete && autocompleteResults.length > 0 && (
          <div className="absolute top-full left-0 right-0 bg-gray-800 border border-gray-700 rounded-lg mt-1 shadow-xl z-50 max-h-64 overflow-y-auto">
            <div className="p-2 border-b border-gray-700">
              <span className="text-xs text-gray-400 font-medium">Suggestions</span>
            </div>
            {autocompleteResults.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleAutocompleteSelect(suggestion)}
                className={`w-full text-left px-4 py-3 transition-colors border-b border-gray-700/50 last:border-b-0 flex items-center justify-between group ${
                  index === selectedSuggestionIndex
                    ? 'bg-blue-600 text-white'
                    : 'hover:bg-gray-700 text-gray-300 hover:text-white'
                }`}
              >
                <span className={index === selectedSuggestionIndex ? 'text-white' : 'text-gray-300 group-hover:text-white'}>
                  {suggestion}
                </span>
                <Search className={`h-3 w-3 ${
                  index === selectedSuggestionIndex
                    ? 'text-blue-200'
                    : 'text-gray-500 group-hover:text-gray-400'
                }`} />
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Filter Controls */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center justify-center sm:justify-start space-x-2 px-4 py-2 rounded-lg border transition-colors ${
              showFilters
                ? 'bg-blue-600 border-blue-500 text-white'
                : 'bg-gray-800 border-gray-700 text-gray-300 hover:bg-gray-700'
            }`}
          >
            <SlidersHorizontal className="h-4 w-4" />
            <span>Filters</span>
            {activeFiltersCount > 0 && (
              <span className="bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                {activeFiltersCount}
              </span>
            )}
          </button>

          {/* Clear Filters */}
          {activeFiltersCount > 0 && (
            <button
              onClick={clearFilters}
              className="flex items-center justify-center space-x-1 px-3 py-2 text-sm text-red-400 hover:text-red-300 transition-colors border border-red-400/30 rounded-lg hover:bg-red-400/10"
            >
              <X className="h-3 w-3" />
              <span>Clear all filters</span>
            </button>
          )}
        </div>

        {/* Sort Options */}
        <div className="flex flex-col sm:flex-row sm:items-center gap-3">
          <span className="text-gray-400 text-sm font-medium">Sort by:</span>
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'stars', label: 'Stars', icon: Star },
              { key: 'forks', label: 'Forks', icon: GitBranch },
              { key: 'updated', label: 'Updated', icon: Calendar },
              { key: 'contributors', label: 'Contributors', icon: Users },
              { key: 'title', label: 'Name', icon: null }
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => handleSortChange(key)}
                className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm transition-colors ${
                  sortBy === key
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700 border border-gray-700'
                }`}
              >
                {Icon && <Icon className="h-3 w-3" />}
                <span>{label}</span>
                {sortBy === key && (
                  <ChevronDown className={`h-3 w-3 transition-transform ${sortOrder === 'asc' ? 'rotate-180' : ''}`} />
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Advanced Filters Panel */}
      {showFilters && (
        <div className="bg-gray-800 rounded-lg p-4 sm:p-6 border border-gray-700 animate-in slide-in-from-top-2 duration-200">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">

            {/* Languages Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-300 mb-3">Programming Language</h3>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {languages.map(language => (
                  <label key={language} className="flex items-center space-x-3 cursor-pointer hover:bg-gray-700/50 p-2 rounded transition-colors">
                    <input
                      type="checkbox"
                      checked={filters.languages.includes(language)}
                      onChange={() => toggleFilter('languages', language)}
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <span className="text-gray-300 text-sm flex-1">{language}</span>
                    <span className="text-gray-500 text-xs bg-gray-700 px-2 py-1 rounded">
                      {projects.filter(p => p.language === language).length}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Difficulty Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-300 mb-3">Difficulty Level</h3>
              <div className="space-y-2">
                {difficulties.map(difficulty => (
                  <label key={difficulty} className="flex items-center space-x-3 cursor-pointer hover:bg-gray-700/50 p-2 rounded transition-colors">
                    <input
                      type="checkbox"
                      checked={filters.difficulties.includes(difficulty)}
                      onChange={() => toggleFilter('difficulties', difficulty)}
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <span className={`text-sm flex-1 font-medium ${
                      difficulty === 'Beginner' ? 'text-green-400' :
                      difficulty === 'Intermediate' ? 'text-yellow-400' : 'text-red-400'
                    }`}>
                      {difficulty}
                    </span>
                    <span className="text-gray-500 text-xs bg-gray-700 px-2 py-1 rounded">
                      {projects.filter(p => p.difficulty === difficulty).length}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Domain Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-300 mb-3">Domain</h3>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {domains.map(domain => (
                  <label key={domain} className="flex items-center space-x-3 cursor-pointer hover:bg-gray-700/50 p-2 rounded transition-colors">
                    <input
                      type="checkbox"
                      checked={filters.domains.includes(domain)}
                      onChange={() => toggleFilter('domains', domain)}
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <span className="text-gray-300 text-sm flex-1">{domain}</span>
                    <span className="text-gray-500 text-xs bg-gray-700 px-2 py-1 rounded">
                      {projects.filter(p => p.domain === domain).length}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Activity Level Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-300 mb-3">Activity Level</h3>
              <div className="space-y-2">
                {activityLevels.map(level => (
                  <label key={level} className="flex items-center space-x-3 cursor-pointer hover:bg-gray-700/50 p-2 rounded transition-colors">
                    <input
                      type="checkbox"
                      checked={filters.activityLevels.includes(level)}
                      onChange={() => toggleFilter('activityLevels', level)}
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <span className={`text-sm flex-1 font-medium ${
                      level === 'High' ? 'text-green-400' :
                      level === 'Medium' ? 'text-yellow-400' : 'text-gray-400'
                    }`}>
                      {level}
                    </span>
                    <span className="text-gray-500 text-xs bg-gray-700 px-2 py-1 rounded">
                      {projects.filter(p => p.activityLevel === level).length}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Stars Range Filter */}
            <div>
              <h3 className="text-sm font-medium text-gray-300 mb-3">Stars Range</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-xs text-gray-400 mb-1">Minimum Stars</label>
                  <input
                    type="number"
                    min="0"
                    value={filters.minStars}
                    onChange={(e) => setFilters(prev => ({ ...prev, minStars: parseInt(e.target.value) || 0 }))}
                    className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-1 text-sm text-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-400 mb-1">Maximum Stars</label>
                  <input
                    type="number"
                    min="0"
                    value={filters.maxStars}
                    onChange={(e) => setFilters(prev => ({ ...prev, maxStars: parseInt(e.target.value) || 1000 }))}
                    className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-1 text-sm text-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Additional Filters */}
            <div>
              <h3 className="text-sm font-medium text-gray-300 mb-3">Additional Filters</h3>
              <div className="space-y-2">
                <label className="flex items-center space-x-3 cursor-pointer hover:bg-gray-700/50 p-2 rounded transition-colors">
                  <input
                    type="checkbox"
                    checked={filters.hasIssues}
                    onChange={(e) => setFilters(prev => ({ ...prev, hasIssues: e.target.checked }))}
                    className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <span className="text-gray-300 text-sm flex-1">Has open issues</span>
                  <span className="text-orange-400 text-xs">Good for contributors</span>
                </label>
                <label className="flex items-center space-x-3 cursor-pointer hover:bg-gray-700/50 p-2 rounded transition-colors">
                  <input
                    type="checkbox"
                    checked={filters.recentlyUpdated}
                    onChange={(e) => setFilters(prev => ({ ...prev, recentlyUpdated: e.target.checked }))}
                    className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <span className="text-gray-300 text-sm flex-1">Recently updated</span>
                  <span className="text-green-400 text-xs">Active projects</span>
                </label>
              </div>
            </div>
          </div>

          {/* Popular Tags */}
          <div className="mt-6 pt-6 border-t border-gray-700">
            <h3 className="text-sm font-medium text-gray-300 mb-3">Popular Tags</h3>
            <div className="flex flex-wrap gap-2">
              {allTags.slice(0, 15).map(tag => (
                <button
                  key={tag}
                  onClick={() => toggleFilter('tags', tag)}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    filters.tags.includes(tag)
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {tag}
                  <span className="ml-1 text-xs opacity-75">
                    ({projects.filter(p => p.tags.includes(tag)).length})
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
