// Main application exports for easy importing

// Components
export * from './components/ui';
export * from './components/layout/Layout';

// Hooks
export * from './hooks';

// Contexts
export * from './contexts/AuthContext';

// Services
export * from './services/authService';
export * from './services/contributionService';

// Types - using specific exports to avoid conflicts
export type {
  User,
  UserStats,
  UserPreferences,
  Contribution,
  Badge as UserBadge,
  UserBadge as UserBadgeInstance,
  BookmarkedProject,
  ActivityFeed,
  GitHubUser,
  GitHubRepo,
  AuthState,
  LoginResponse,
  ContributionResponse,
  BadgeResponse
} from './types/userTypes';

export type {
  Project,
  Issue,
  Maintainer,
  Commit,
  SearchFilters as ProjectSearchFilters,
  SearchResult as ProjectSearchResult
} from './types/project.types';

// Constants
export * from './constants';

// Utils
export * from './utils/demoData';
